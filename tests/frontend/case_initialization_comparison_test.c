// Test to compare different initialization patterns
int main() {
    // Test 1: Full initialization vs Empty initialization
    int full_init[5] = {1, 2, 3, 4, 5};     // All elements specified
    int empty_init[5] = {};                  // All elements should be 0
    
    float full_float[3] = {1.1, 2.2, 3.3};  // All elements specified
    float empty_float[3] = {};               // All elements should be 0.0
    
    // Test 2: Partial initialization vs Empty initialization
    int partial_init[5] = {1, 2, 3};        // First 3 elements specified, rest should be 0
    int empty_init2[5] = {};                 // All elements should be 0
    
    float partial_float[4] = {1.1, 2.2};    // First 2 elements specified, rest should be 0.0
    float empty_float2[4] = {};              // All elements should be 0.0
    
    // Test 3: 2D array comparisons
    int full_2d[2][3] = {{1, 2, 3}, {4, 5, 6}};    // All elements specified
    int empty_2d[2][3] = {};                         // All elements should be 0
    int partial_2d[2][3] = {{1, 2}};                // Partial initialization
    
    float full_2d_float[2][2] = {{1.1, 1.2}, {2.1, 2.2}};  // All elements specified
    float empty_2d_float[2][2] = {};                         // All elements should be 0.0
    
    // Test 4: Constant arrays
    const int const_full[3] = {10, 20, 30};     // All elements specified
    const int const_empty[3] = {};              // All elements should be 0
    
    const float const_full_float[2] = {3.14, 2.71};  // All elements specified
    const float const_empty_float[2] = {};            // All elements should be 0.0
    
    // Test 5: Access and verify values
    // empty_init[0] should be 0, full_init[0] should be 1
    // empty_float[0] should be 0.0, full_float[0] should be 1.1
    int result = full_init[0] + empty_init[0];  // Should be 1 + 0 = 1
    
    return result;
}
