
// Generated from /home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/grammar/SysY2022.g4 by ANTLR 4.7.2

#pragma once


#include "antlr4-runtime.h"




class  SysY2022Lexer : public antlr4::Lexer {
public:
  enum {
    INT = 1, FLOAT = 2, VOID = 3, CONST = 4, IF = 5, ELSE = 6, WHILE = 7, 
    BREAK = 8, CONTINUE = 9, RETURN = 10, SEMICOLON = 11, COMMA = 12, LPAREN = 13, 
    RPAREN = 14, LBRACE = 15, RBRACE = 16, LBRACKET = 17, RBRACKET = 18, 
    ASSIGN = 19, ADD = 20, SUB = 21, MUL = 22, DIV = 23, MOD = 24, LT = 25, 
    GT = 26, LE = 27, GE = 28, EQ = 29, NE = 30, AND = 31, OR = 32, NOT = 33, 
    IDENT = 34, WHIESPACE = 35, LINECOMMENT = 36, BLOCKCOMMENT = 37, INTCONST = 38, 
    FLOATCONST = 39
  };

  SysY2022Lexer(antlr4::CharStream *input);
  ~SysY2022Lexer();

  virtual std::string getGrammarFileName() const override;
  virtual const std::vector<std::string>& getRuleNames() const override;

  virtual const std::vector<std::string>& getChannelNames() const override;
  virtual const std::vector<std::string>& getModeNames() const override;
  virtual const std::vector<std::string>& getTokenNames() const override; // deprecated, use vocabulary instead
  virtual antlr4::dfa::Vocabulary& getVocabulary() const override;

  virtual const std::vector<uint16_t> getSerializedATN() const override;
  virtual const antlr4::atn::ATN& getATN() const override;

private:
  static std::vector<antlr4::dfa::DFA> _decisionToDFA;
  static antlr4::atn::PredictionContextCache _sharedContextCache;
  static std::vector<std::string> _ruleNames;
  static std::vector<std::string> _tokenNames;
  static std::vector<std::string> _channelNames;
  static std::vector<std::string> _modeNames;

  static std::vector<std::string> _literalNames;
  static std::vector<std::string> _symbolicNames;
  static antlr4::dfa::Vocabulary _vocabulary;
  static antlr4::atn::ATN _atn;
  static std::vector<uint16_t> _serializedATN;


  // Individual action functions triggered by action() above.

  // Individual semantic predicate functions triggered by sempred() above.

  struct Initializer {
    Initializer();
  };
  static Initializer _init;
};

