[{"arguments": ["/usr/bin/g++", "-std=c++17", "-Igenerated_front_end", "-I/usr/include/antlr4-runtime", "-c", "-o", "generated_front_end/SysY2022BaseListener.o", "generated_front_end/SysY2022BaseListener.cpp"], "directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022BaseListener.cpp", "output": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022BaseListener.o"}, {"arguments": ["/usr/bin/g++", "-std=c++17", "-Igenerated_front_end", "-I/usr/include/antlr4-runtime", "-c", "-o", "generated_front_end/SysY2022BaseVisitor.o", "generated_front_end/SysY2022BaseVisitor.cpp"], "directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022BaseVisitor.cpp", "output": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022BaseVisitor.o"}, {"arguments": ["/usr/bin/g++", "-std=c++17", "-Igenerated_front_end", "-I/usr/include/antlr4-runtime", "-c", "-o", "generated_front_end/SysY2022Lexer.o", "generated_front_end/SysY2022Lexer.cpp"], "directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022Lexer.cpp", "output": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022Lexer.o"}, {"arguments": ["/usr/bin/g++", "-std=c++17", "-Igenerated_front_end", "-I/usr/include/antlr4-runtime", "-c", "-o", "generated_front_end/SysY2022Listener.o", "generated_front_end/SysY2022Listener.cpp"], "directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022Listener.cpp", "output": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022Listener.o"}, {"arguments": ["/usr/bin/g++", "-std=c++17", "-Igenerated_front_end", "-I/usr/include/antlr4-runtime", "-c", "-o", "generated_front_end/SysY2022Parser.o", "generated_front_end/SysY2022Parser.cpp"], "directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022Parser.cpp", "output": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022Parser.o"}, {"arguments": ["/usr/bin/g++", "-std=c++17", "-Igenerated_front_end", "-I/usr/include/antlr4-runtime", "-c", "-o", "generated_front_end/SysY2022Visitor.o", "generated_front_end/SysY2022Visitor.cpp"], "directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022Visitor.cpp", "output": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/generated_front_end/SysY2022Visitor.o"}, {"arguments": ["/usr/bin/g++", "-std=c++17", "-Igenerated_front_end", "-I/usr/include/antlr4-runtime", "-c", "-o", "src/main.o", "src/main.cpp"], "directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/src/main.cpp", "output": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/antlr4/src/main.o"}]