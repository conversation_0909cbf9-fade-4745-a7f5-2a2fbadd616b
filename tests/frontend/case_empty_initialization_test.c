// Test empty initialization: int a[5] = {}; float b[3] = {};
int main() {
    // Test 1: Empty initialization for int arrays
    int int_arr1[5] = {};           // Should initialize all elements to 0
    int int_arr2[10] = {};          // Larger array
    int int_arr3[1] = {};           // Single element
    
    // Test 2: Empty initialization for float arrays
    float float_arr1[3] = {};       // Should initialize all elements to 0.0
    float float_arr2[7] = {};       // Larger array
    float float_arr3[1] = {};       // Single element
    
    // Test 3: Empty initialization for 2D int arrays
    int int_matrix1[2][3] = {};     // Should initialize all elements to 0
    int int_matrix2[3][2] = {};
    
    // Test 4: Empty initialization for 2D float arrays
    float float_matrix1[2][2] = {}; // Should initialize all elements to 0.0
    float float_matrix2[3][3] = {};
    
    // Test 5: Empty initialization for 3D arrays
    int int_cube[2][2][2] = {};     // Should initialize all elements to 0
    float float_cube[2][2][2] = {}; // Should initialize all elements to 0.0
    
    // Test 6: Constant arrays with empty initialization
    const int const_int_arr[4] = {};        // Should initialize all elements to 0
    const float const_float_arr[3] = {};    // Should initialize all elements to 0.0
    
    // Test 7: Constant 2D arrays with empty initialization
    const int const_int_matrix[2][2] = {};      // Should initialize all elements to 0
    const float const_float_matrix[2][2] = {};  // Should initialize all elements to 0.0
    
    // Test 8: Mixed declarations with empty initialization
    int a = 42, b[5] = {}, c = 10;
    float x = 3.14, y[3] = {}, z = 2.71;
    
    // Test 9: Access elements to verify they are zero-initialized
    int sum = int_arr1[0] + int_arr1[4] + int_matrix1[1][2];
    float fsum = float_arr1[0] + float_arr1[2] + float_matrix1[1][1];
    
    return sum; // Should return 0 since all elements are zero-initialized
}
