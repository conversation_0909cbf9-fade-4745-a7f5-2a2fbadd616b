%{
  #include <iostream>
  using namespace std;
  extern int yylex();
%}
%option noyywrap
%%
[ \t]           ;
[\n]            ; /* could use { ++linenum; } to increment line count */
[0-9]+\.[0-9]+  { cout << "Found a floating-point number:" << yytext << endl; }
[0-9]+          { cout << "Found an integer:" << yytext << endl; }
[a-zA-Z0-9]+    { cout << "Found a string: " << yytext << endl; }
.               { cout << "Found undefined token: " << yytext << endl; }
%%
int main(int, char**) {

    // open a file hanle to a particular file:
    FILE *src_file = fopen("file.name", "r");
    if (!src_file) {
        cout << "Invalid file!" << endl;
        return -1;
    }
    // set lex to read from it instead of defaulting to STDIN:
    yyin = src_file;

    // lex through the input:
    while (yylex());
}