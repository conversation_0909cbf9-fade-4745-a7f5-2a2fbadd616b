#include "ASTVisitor.hpp"
#include "Type.hpp"

ASTVisitor::ASTVisitor() {
    llvm_context = std::make_unique<llvm::LLVMContext>();
    llvm_module = std::make_unique<llvm::Module>("SysY2022", *llvm_context);
    llvm_builder = std::make_unique<llvm::IRBuilder<>>(*llvm_context);
}

void ASTVisitor::print_llvm_ir() {
    llvm_module->print(llvm::outs(), nullptr);
}

antlrcpp::Any ASTVisitor::visitCompUnit(SysY2022Parser::CompUnitContext *ctx) {
    printIndent();
    std::cout << "CompUnit" << std::endl;
    indent++;
    
    for (auto child : ctx->children) {
        visit(child);
    }
    
    indent--;
    return nullptr;
}

antlrcpp::Any ASTVisitor::visitFuncDef(SysY2022Parser::FuncDefContext *ctx) {
    printIndent();
    std::cout << "FuncDef: " << ctx->IDENT()->getText() << std::endl;
    indent++;
    
    // Create function type (int main())
    // TODO: treat parameters
    llvm::FunctionType *funcType = get_function_type(ctx, *llvm_context); 
    
    // Create function
    // TODO: treat linkage
    llvm::Function *function = llvm::Function::Create(
        funcType, llvm::Function::ExternalLinkage, 
        ctx->IDENT()->getText(), llvm_module.get());
    
    // Create basic block
    llvm::BasicBlock *bb = llvm::BasicBlock::Create( *llvm_context, "entry", function);
    llvm_builder->SetInsertPoint(bb);
    
    // Visit function body
    visit(ctx->block());
    
    indent--;
    return nullptr;
}

antlrcpp::Any ASTVisitor::visitBlock(SysY2022Parser::BlockContext *ctx) {
    printIndent();
    std::cout << "Block" << std::endl;
    indent++;
    
    for (auto item : ctx->blockItem()) {
        visit(item);
    }
    
    indent--;
    return nullptr;
}

antlrcpp::Any ASTVisitor::visitVarDecl(SysY2022Parser::VarDeclContext *ctx) {
    printIndent();
    std::cout << "VarDecl" << std::endl;
    indent++;
    
    // Create alloca for each variable
    for (auto var_def : ctx->varDef()) {
        std::string var_name = var_def->IDENT()->getText();
        llvm::AllocaInst *alloca = llvm_builder->CreateAlloca(
            llvm::Type::getInt32Ty(*llvm_context), nullptr, var_name);
        named_values[var_name] = alloca;
        
        printIndent();
        std::cout << "Variable: " << var_name << std::endl;
    }
    
    indent--;
    return nullptr;
}

antlrcpp::Any ASTVisitor::visitStmt(SysY2022Parser::StmtContext *ctx) {
    printIndent();
    std::cout << "Statement" << std::endl;
    indent++;
    
    if (ctx->ASSIGN()) {
        // Assignment: a = 10
        std::string varName = ctx->lVal()->IDENT()->getText();
        llvm::Value *val = visit(ctx->exp());
        llvm::Value *var = named_values[varName];
        llvm_builder->CreateStore(val, var);
    } else if (ctx->RETURN()) {
        // Return statement
        if (ctx->exp()) {
            llvm::Value *retVal = visit(ctx->exp());
            llvm_builder->CreateRet(retVal);
        } else {
            llvm_builder->CreateRetVoid();
        }
    }
    
    indent--;
    return nullptr;
}

antlrcpp::Any ASTVisitor::visitExp(SysY2022Parser::ExpContext *ctx) {
    return visit(ctx->addExp());
}

antlrcpp::Any ASTVisitor::visitAddExp(SysY2022Parser::AddExpContext *ctx) {
    if (ctx->addExp()) {
        // Binary operation: a + b
        llvm::Value *left = visit(ctx->addExp());
        llvm::Value *right = visit(ctx->mulExp());
        
        if (ctx->ADD()) {
            return static_cast<llvm::Value*>(llvm_builder->CreateAdd(left, right, "addtmp"));
        } else if (ctx->SUB()) {
            return static_cast<llvm::Value*>(llvm_builder->CreateSub(left, right, "subtmp"));
        }
    }
    
    // Single operand
    return visit(ctx->mulExp());
}

antlrcpp::Any ASTVisitor::visitNumber(SysY2022Parser::NumberContext *ctx) {
    int value = std::stoi(ctx->INTCONST()->getText());
    return static_cast<llvm::Value*>(
        llvm::ConstantInt::get(llvm::Type::getInt32Ty(*llvm_context), value));
}

antlrcpp::Any ASTVisitor::visitLVal(SysY2022Parser::LValContext *ctx) {
    std::string varName = ctx->IDENT()->getText();
    llvm::Value *var = named_values[varName];
    return static_cast<llvm::Value*>(llvm_builder->CreateLoad(
        llvm::Type::getInt32Ty(*llvm_context), var, varName));
}
