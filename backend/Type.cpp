#include "Type.hpp"

llvm::FunctionType* get_llvm_type(
    SysY2022Parser::FuncTypeContext *ctx,
    llvm::LLVMContext& lctx
) {
    
    llvm::Type* ty;
    if (ctx->bType()) {
        ty = get_llvm_type(ctx->bType(), lctx);
    } else {
        ty = llvm::Type::getVoidTy(lctx);
    }
    // llvm::Type::TypeID type_id = get_llvm_type_id(ctx->funcType());
    // llvm::Type *return_type = llvm::Type::getPrimitiveType(llvm_context, type_id);
    return llvm::FunctionType::get(ty, false);
}

llvm::Type* get_llvm_type(
    SysY2022Parser::BTypeContext *ctx,
    llvm::LLVMContext& lctx
)
{
    if (ctx->INT()) {
        return llvm::Type::getInt32Ty(lctx);
    } else if (ctx->FLOAT()) {
        return llvm::Type::getFloatTy(lctx);
    } else {
        // this should not happen
        // TODO: throw an error
        return llvm::Type::getVoidTy(lctx);
    }
}