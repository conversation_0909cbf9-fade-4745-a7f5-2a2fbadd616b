// Test error cases for constant arrays (these should produce meaningful error messages)
int main() {
    // Test 1: Array size mismatch (more initializers than size)
    // const int mismatch[3] = {1, 2, 3, 4, 5}; // Should error
    
    // Test 2: Non-constant array size (should error in const context)
    // int n = 5;
    // const int var_size[n] = {1, 2, 3, 4, 5}; // Should error
    
    // Test 3: Non-constant initializer values (should error)
    // int x = 10;
    // const int non_const[2] = {x, 20}; // Should error
    
    // Test 4: Missing initialization for const array (should error)
    // const int no_init[5]; // Should error
    
    // Valid cases for comparison:
    const int valid1[3] = {1, 2, 3};
    const int valid2[5] = {1, 2}; // Partial init OK
    const int valid3[2] = {}; // Empty init OK
    
    return valid1[0] + valid2[0] + valid3[0];
}

// Test 5: Global constant without initialization (should error)
// const int global_no_init[3]; // Should error
