#include "Type.hpp"

llvm::FunctionType* get_function_type(
    SysY2022Parser::FuncDefContext *ctx,
    llvm::LLVMContext& llvm_context
) {
    llvm::Type::TypeID type_id = get_llvm_type_id(ctx->funcType());
    llvm::Type *return_type = llvm::Type::getPrimitiveType(llvm_context, type_id);
    return llvm::FunctionType::get(return_type, false);
}





llvm::Type::TypeID get_llvm_type_id(SysY2022Parser::BTypeContext *ctx)
{
    if (ctx->INT()) {
        return llvm::Type::IntegerTyID;
    } else if (ctx->FLOAT()) {
        return llvm::Type::FloatTyID;
    } else {
        // this should not happen
        // TODO: throw an error
        return llvm::Type::VoidTyID;
    }
}

llvm::Type::TypeID get_llvm_type_id(SysY2022Parser::FuncTypeContext *ctx)
{
    if (ctx->bType()) {
        return get_llvm_type_id(ctx->bType());
    } else {
        return llvm::Type::VoidTyID;
    }
}