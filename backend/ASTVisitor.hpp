#pragma once
#include "antlr4-runtime.h"
#include "SysY2022BaseVisitor.h"
#include <iostream>
#include <llvm-14/llvm/ADT/STLFunctionalExtras.h>
#include <llvm-14/llvm/IR/DerivedTypes.h>
#include <memory>

// LLVM includes
#include "llvm/IR/LLVMContext.h"
#include "llvm/IR/Module.h"
#include "llvm/IR/IRBuilder.h"
#include "llvm/IR/Value.h"
#include "llvm/IR/Function.h"
#include "llvm/IR/BasicBlock.h"
#include "llvm/IR/Type.h"
#include "llvm/IR/Verifier.h"
#include "llvm/Support/raw_ostream.h"
// Pass related includes
#include "llvm/IR/PassManager.h"
#include "llvm/Passes/PassBuilder.h"
#include "llvm/Passes/StandardInstrumentations.h"
#include "llvm/Transforms/InstCombine/InstCombine.h"
#include "llvm/Transforms/Scalar.h"
#include "llvm/Transforms/Scalar/GVN.h"
#include "llvm/Transforms/Scalar/Reassociate.h"
#include "llvm/Transforms/Scalar/SimplifyCFG.h"
#include "llvm/Transforms/Utils/Mem2Reg.h"
#include "llvm/IR/LegacyPassManager.h"
#include "llvm/Transforms/Scalar.h"


class ASTVisitor : public SysY2022BaseVisitor {
private:
    int indent = 0;

    // LLVM components
    std::unique_ptr<llvm::LLVMContext> llvm_context;
    std::unique_ptr<llvm::Module> llvm_module;
    std::unique_ptr<llvm::IRBuilder<>> llvm_builder;
    
    // Symbol table for variables
    std::map<std::string, std::vector<llvm::Value*>> named_values;


    std::map<std::string, llvm::Function*> function_table;
    // llvm::BasicBlock* current_block = nullptr;
    llvm::Function* current_function = nullptr;

    // LLVM pass and analysis managers:
    static std::unique_ptr<llvm::FunctionPassManager> function_pass_manager;
    static std::unique_ptr<llvm::LoopAnalysisManager> loop_analysis_manager;
    static std::unique_ptr<llvm::FunctionAnalysisManager> function_analysis_manager;
    static std::unique_ptr<llvm::CGSCCAnalysisManager> cgscc_analysis_manager;
    static std::unique_ptr<llvm::ModuleAnalysisManager> module_analysis_manager;
    static std::unique_ptr<llvm::PassInstrumentationCallbacks> pass_instrumentation_callbacks;
    static std::unique_ptr<llvm::StandardInstrumentations> standard_instrumentations;


    llvm::Type* current_type = nullptr;
    llvm::ArrayType* current_array_type = nullptr;

    void printIndent() {
        for (int i = 0; i < indent; i++) std::cout << "  ";
    }

    // Helper function to create zero-initialized array constants
    llvm::Constant* createZeroInitializedArray(llvm::Type* baseType, const std::vector<int>& dimensions);

    // Scope management helpers
    void pushScope();
    void popScope();

    // Loop context management helpers
    void pushLoop(llvm::BasicBlock* break_target, llvm::BasicBlock* continue_target, const std::string& loop_name);
    void popLoop();
    llvm::BasicBlock* getCurrentBreakTarget();
    llvm::BasicBlock* getCurrentContinueTarget();

private:
    // Stack of symbol tables for nested scopes
    std::vector<std::map<std::string, std::vector<llvm::Value*>>> scope_stack;

    // Loop context for break/continue statements
    struct LoopContext {
        llvm::BasicBlock* break_target;    // Where to jump on break
        llvm::BasicBlock* continue_target; // Where to jump on continue
        std::string loop_name;             // For debugging
    };
    std::vector<LoopContext> loop_stack;

    // Initialize SysY runtime library functions (external declarations only)
    void initializeRuntimeLibrary();

public:
    ASTVisitor();

    // Run optimization passes including mem2reg
    void optimize_ir();
    void generate_llvm_ir();
    void print_llvm_ir();


    antlrcpp::Any visitCompUnit(SysY2022Parser::CompUnitContext *ctx) override;
    antlrcpp::Any visitProgram(SysY2022Parser::ProgramContext *ctx) override;
    antlrcpp::Any visitDecl(SysY2022Parser::DeclContext *ctx) override;
    antlrcpp::Any visitConstDecl(SysY2022Parser::ConstDeclContext *ctx) override;
    // antlrcpp::Any visitBType(SysY2022Parser::BTypeContext *ctx) override;
    antlrcpp::Any visitConstDef(SysY2022Parser::ConstDefContext *ctx) override;
    antlrcpp::Any visitConstInitVal(SysY2022Parser::ConstInitValContext *ctx) override;
    antlrcpp::Any visitVarDecl(SysY2022Parser::VarDeclContext *ctx) override;
    // antlrcpp::Any visitVarDef(SysY2022Parser::VarDefContext *ctx) override;
    antlrcpp::Any visitInitVal(SysY2022Parser::InitValContext *ctx) override;
    antlrcpp::Any visitFuncDef(SysY2022Parser::FuncDefContext *ctx) override;
    antlrcpp::Any visitFuncType(SysY2022Parser::FuncTypeContext *ctx) override;
    antlrcpp::Any visitFuncFormalParams(SysY2022Parser::FuncFormalParamsContext *ctx) override;
    antlrcpp::Any visitFuncFormalParam(SysY2022Parser::FuncFormalParamContext *ctx) override;
    antlrcpp::Any visitFuncRealParams(SysY2022Parser::FuncRealParamsContext *ctx) override;
    antlrcpp::Any visitBlock(SysY2022Parser::BlockContext *ctx) override;
    // antlrcpp::Any visitBlockItem(SysY2022Parser::BlockItemContext *ctx) override;
    antlrcpp::Any visitStmt(SysY2022Parser::StmtContext *ctx) override;
    antlrcpp::Any visitExp(SysY2022Parser::ExpContext *ctx) override;
    // antlrcpp::Any visitCond(SysY2022Parser::CondContext *ctx) override;
    antlrcpp::Any visitLVal(SysY2022Parser::LValContext *ctx) override;
    antlrcpp::Any visitPrimaryExp(SysY2022Parser::PrimaryExpContext *ctx) override;
    antlrcpp::Any visitNumber(SysY2022Parser::NumberContext *ctx) override;
    antlrcpp::Any visitUnaryExp(SysY2022Parser::UnaryExpContext *ctx) override;
    // antlrcpp::Any visitUnaryOP(SysY2022Parser::UnaryOPContext *ctx) override;
    // antlrcpp::Any funcRealParams(SysY2022Parser::MulExpContext *ctx) override;
    antlrcpp::Any visitMulExp(SysY2022Parser::MulExpContext *ctx) override;
    antlrcpp::Any visitAddExp(SysY2022Parser::AddExpContext *ctx) override;
    // antlrcpp::Any visitRelExp(SysY2022Parser::RelExpContext *ctx) override;
    // antlrcpp::Any visitEqExp(SysY2022Parser::EqExpContext *ctx) override;
    // antlrcpp::Any visitLAndExp(SysY2022Parser::LAndExpContext *ctx) override;
    // antlrcpp::Any visitLOrExp(SysY2022Parser::LOrExpContext *ctx) override;
    antlrcpp::Any visitConstExp(SysY2022Parser::ConstExpContext *ctx) override;

};