#pragma once

#include "llvm/IR/Type.h"
#include "llvm/IR/LLVMContext.h"
#include "llvm/IR/DerivedTypes.h"
#include "../generated_front_end/SysY2022Parser.h"

llvm::FunctionType* get_function_type(
    SysY2022Parser::FuncDefContext *ctx,
    llvm::LLVMContext& llvm_context);

llvm::Type::TypeID get_llvm_type_id(SysY2022Parser::BTypeContext *ctx);
llvm::Type::TypeID get_llvm_type_id(SysY2022Parser::FuncTypeContext *ctx);