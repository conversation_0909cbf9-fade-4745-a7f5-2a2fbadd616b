
// Generated from SysY2022.g4 by ANTLR 4.7.2

#pragma once


#include "antlr4-runtime.h"
#include "SysY2022Parser.h"



/**
 * This class defines an abstract visitor for a parse tree
 * produced by SysY2022Parser.
 */
class  SysY2022Visitor : public antlr4::tree::AbstractParseTreeVisitor {
public:

  /**
   * Visit parse trees produced by SysY2022Parser.
   */
    virtual antlrcpp::Any visitProgram(SysY2022Parser::ProgramContext *context) = 0;

    virtual antlrcpp::Any visitCompUnit(SysY2022Parser::CompUnitContext *context) = 0;

    virtual antlrcpp::Any visitDecl(SysY2022Parser::DeclContext *context) = 0;

    virtual antlrcpp::Any visitConstDecl(SysY2022Parser::ConstDeclContext *context) = 0;

    virtual antlrcpp::Any visitBType(SysY2022Parser::BTypeContext *context) = 0;

    virtual antlrcpp::Any visitConstDef(SysY2022Parser::ConstDefContext *context) = 0;

    virtual antlrcpp::Any visitConstInitVal(SysY2022Parser::ConstInitValContext *context) = 0;

    virtual antlrcpp::Any visitVarDecl(SysY2022Parser::VarDeclContext *context) = 0;

    virtual antlrcpp::Any visitVarDef(SysY2022Parser::VarDefContext *context) = 0;

    virtual antlrcpp::Any visitInitVal(SysY2022Parser::InitValContext *context) = 0;

    virtual antlrcpp::Any visitFuncDef(SysY2022Parser::FuncDefContext *context) = 0;

    virtual antlrcpp::Any visitFuncType(SysY2022Parser::FuncTypeContext *context) = 0;

    virtual antlrcpp::Any visitFuncFormalParams(SysY2022Parser::FuncFormalParamsContext *context) = 0;

    virtual antlrcpp::Any visitFuncFormalParam(SysY2022Parser::FuncFormalParamContext *context) = 0;

    virtual antlrcpp::Any visitBlock(SysY2022Parser::BlockContext *context) = 0;

    virtual antlrcpp::Any visitBlockItem(SysY2022Parser::BlockItemContext *context) = 0;

    virtual antlrcpp::Any visitStmt(SysY2022Parser::StmtContext *context) = 0;

    virtual antlrcpp::Any visitExp(SysY2022Parser::ExpContext *context) = 0;

    virtual antlrcpp::Any visitCond(SysY2022Parser::CondContext *context) = 0;

    virtual antlrcpp::Any visitLVal(SysY2022Parser::LValContext *context) = 0;

    virtual antlrcpp::Any visitPrimaryExp(SysY2022Parser::PrimaryExpContext *context) = 0;

    virtual antlrcpp::Any visitNumber(SysY2022Parser::NumberContext *context) = 0;

    virtual antlrcpp::Any visitUnaryExp(SysY2022Parser::UnaryExpContext *context) = 0;

    virtual antlrcpp::Any visitUnaryOP(SysY2022Parser::UnaryOPContext *context) = 0;

    virtual antlrcpp::Any visitFuncRealParams(SysY2022Parser::FuncRealParamsContext *context) = 0;

    virtual antlrcpp::Any visitMulExp(SysY2022Parser::MulExpContext *context) = 0;

    virtual antlrcpp::Any visitAddExp(SysY2022Parser::AddExpContext *context) = 0;

    virtual antlrcpp::Any visitRelExp(SysY2022Parser::RelExpContext *context) = 0;

    virtual antlrcpp::Any visitEqExp(SysY2022Parser::EqExpContext *context) = 0;

    virtual antlrcpp::Any visitLAndExp(SysY2022Parser::LAndExpContext *context) = 0;

    virtual antlrcpp::Any visitLOrExp(SysY2022Parser::LOrExpContext *context) = 0;

    virtual antlrcpp::Any visitConstExp(SysY2022Parser::ConstExpContext *context) = 0;


};

