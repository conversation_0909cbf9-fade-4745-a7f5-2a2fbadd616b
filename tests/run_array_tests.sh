#!/bin/bash

# Test runner for constant array test cases
# Usage: ./run_array_tests.sh [compiler_executable]

COMPILER=${1:-"../build/compiler"}
TEST_DIR="frontend"
RESULTS_DIR="results"

# Create results directory if it doesn't exist
mkdir -p $RESULTS_DIR

echo "Running Constant Array Tests..."
echo "================================"

# Array of test files
TEST_FILES=(
    "case_array_const_basic.c"
    "case_array_const_2d.c"
    "case_array_const_expressions.c"
    "case_array_const_global.c"
    "case_array_const_edge_cases.c"
    "case_array_const_errors.c"
    "case_var_decl_test.c"
    "case_float_support_test.c"
    "case_mixed_types_test.c"
    "case_empty_initialization_test.c"
    "case_initialization_comparison_test.c"
    "case_block_scope_test.c"
    "case_control_flow_blocks_test.c"
    "case_block_arrays_test.c"
    "case_break_continue_test.c"
    "case_break_continue_complex_test.c"
    "case_break_continue_edge_cases_test.c"
    "case_hex_oct_numbers_test.c"
    "case_number_formats_edge_cases_test.c"
    "case_function_declarations_test.c"
    "case_function_edge_cases_test.c"
)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

PASSED=0
FAILED=0
TOTAL=0

for test_file in "${TEST_FILES[@]}"; do
    echo ""
    echo "Testing: $test_file"
    echo "----------------------------------------"
    
    TOTAL=$((TOTAL + 1))
    
    # Run the compiler on the test file
    if $COMPILER "$TEST_DIR/$test_file" > "$RESULTS_DIR/${test_file%.c}.out" 2>&1; then
        echo -e "${GREEN}✓ PASSED${NC} - Compilation successful"
        PASSED=$((PASSED + 1))
        
        # Show first few lines of output
        echo "Output preview:"
        head -20 "$RESULTS_DIR/${test_file%.c}.out" | sed 's/^/  /'
        
    else
        echo -e "${RED}✗ FAILED${NC} - Compilation failed"
        FAILED=$((FAILED + 1))
        
        # Show error output
        echo "Error output:"
        tail -10 "$RESULTS_DIR/${test_file%.c}.out" | sed 's/^/  /'
    fi
done

echo ""
echo "================================"
echo "Test Summary:"
echo -e "Total tests: $TOTAL"
echo -e "${GREEN}Passed: $PASSED${NC}"
echo -e "${RED}Failed: $FAILED${NC}"

if [ $FAILED -eq 0 ]; then
    echo -e "${GREEN}All tests passed!${NC}"
    exit 0
else
    echo -e "${YELLOW}Some tests failed. Check results in $RESULTS_DIR/${NC}"
    exit 1
fi
