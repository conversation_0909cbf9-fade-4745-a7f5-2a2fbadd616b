INT=1
FLOAT=2
VOID=3
CONST=4
IF=5
ELSE=6
WHILE=7
BREAK=8
CONTINUE=9
RETURN=10
SEMICOLON=11
COMMA=12
LPAREN=13
RPAREN=14
LBRACE=15
RBRACE=16
LBRACKET=17
RBRACKET=18
ASSIGN=19
ADD=20
SUB=21
MUL=22
DIV=23
MOD=24
LT=25
GT=26
LE=27
GE=28
EQ=29
NE=30
AND=31
OR=32
NOT=33
IDENT=34
WHIESPACE=35
LINECOMMENT=36
BLOCKCOMMENT=37
INTCONST=38
FLOATCONST=39
'int'=1
'float'=2
'void'=3
'const'=4
'if'=5
'else'=6
'while'=7
'break'=8
'continue'=9
'return'=10
';'=11
','=12
'('=13
')'=14
'{'=15
'}'=16
'['=17
']'=18
'='=19
'+'=20
'-'=21
'*'=22
'/'=23
'%'=24
'<'=25
'>'=26
'<='=27
'>='=28
'=='=29
'!='=30
'&&'=31
'||'=32
'!'=33
