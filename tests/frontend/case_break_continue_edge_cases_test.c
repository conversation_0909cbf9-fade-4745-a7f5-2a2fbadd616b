// Test edge cases for break and continue
int main() {
    int result = 0;
    
    // Test 1: Break as first statement in loop
    int i = 0;
    while (i < 100) {
        break;  // Immediate break
    }
    // Loop should not execute body
    
    // Test 2: Continue as first statement in loop
    int j = 0;
    while (j < 3) {
        j = j + 1;
        continue;  // Immediate continue
        result = result + 999;  // This should never execute
    }
    // result should still be 0
    
    // Test 3: Break after continue (unreachable code)
    int k = 0;
    while (k < 5) {
        k = k + 1;
        if (k == 2) {
            continue;
            break;  // Unreachable after continue
        }
        result = result + k;
    }
    // Should add 1+3+4+5 = 13
    
    // Test 4: Multiple breaks in different conditions
    int m = 0;
    while (m < 10) {
        m = m + 1;
        
        if (m == 3) {
            result = result + 100;
            break;  // First break condition
        }
        
        if (m == 7) {
            result = result + 200;
            break;  // Second break condition (unreachable)
        }
        
        result = result + m;
    }
    // Should add 1+2+100 = 103, so result = 13+103 = 116
    
    // Test 5: Multiple continues in different conditions
    int n = 0;
    while (n < 6) {
        n = n + 1;
        
        if (n == 2) {
            continue;  // First continue
        }
        
        if (n == 4) {
            continue;  // Second continue
        }
        
        result = result + n;
    }
    // Should add 1+3+5+6 = 15, so result = 116+15 = 131
    
    // Test 6: Break/continue in deeply nested structure
    int outer = 0;
    while (outer < 2) {
        outer = outer + 1;
        
        {
            int middle = 0;
            while (middle < 3) {
                middle = middle + 1;
                
                {
                    if (outer == 1 && middle == 2) {
                        continue;  // Continue inner loop from deep nesting
                    }
                    
                    if (outer == 2 && middle == 1) {
                        break;  // Break inner loop from deep nesting
                    }
                    
                    result = result + (outer * 10 + middle);
                }
            }
        }
    }
    // Should add 11+13 + 21 = 45, so result = 131+45 = 176
    
    return result;
}
