// Test complex break and continue scenarios
int main() {
    int total = 0;
    
    // Test 1: Break/continue with variable declarations in loops
    int count = 0;
    while (count < 10) {
        count = count + 1;
        int loop_var = count * 3;
        
        if (loop_var > 20) {
            break;  // Break when loop_var > 20 (count >= 7)
        }
        
        if (loop_var == 9) {
            continue;  // Skip when loop_var == 9 (count == 3)
        }
        
        total = total + loop_var;
    }
    // Should add 3+6+12+15+18 = 54
    
    // Test 2: Break/continue in nested blocks with if statements
    int x = 0;
    while (x < 6) {
        x = x + 1;
        
        if (x == 2) {
            {
                int nested = x * 5;
                if (nested == 10) {
                    continue;  // Continue from nested block
                }
            }
        }
        
        if (x == 5) {
            {
                int check = x + 1;
                if (check == 6) {
                    break;  // Break from nested block
                }
            }
        }
        
        total = total + x;
    }
    // Should add 1+3+4 = 8, so total = 54+8 = 62
    
    // Test 3: Multiple nested loops with different break/continue patterns
    int a = 0;
    while (a < 3) {
        a = a + 1;
        int b = 0;
        
        while (b < 4) {
            b = b + 1;
            
            // Skip certain combinations
            if (a == 1 && b == 2) {
                continue;  // Continue inner loop
            }
            
            // Break inner loop for specific condition
            if (a == 2 && b == 3) {
                break;  // Break inner loop only
            }
            
            // Add to total
            total = total + (a * 10 + b);
            
            // Nested condition with break
            if (a == 3) {
                if (b == 2) {
                    break;  // Break inner loop when a==3, b==2
                }
            }
        }
    }
    // Should add: (11+13+14) + (21+22+24) + (31+32) = 38+67+63 = 168
    // So total = 62+168 = 230
    
    // Test 4: Break/continue with array access
    int arr[5] = {1, 2, 3, 4, 5};
    int idx = 0;
    while (idx < 5) {
        int value = arr[idx];
        idx = idx + 1;
        
        if (value == 2) {
            continue;  // Skip value 2
        }
        
        if (value == 4) {
            break;  // Break when value is 4
        }
        
        total = total + value;
    }
    // Should add 1+3 = 4, so total = 230+4 = 234
    
    return total;
}
