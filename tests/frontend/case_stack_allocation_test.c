// Test stack allocation technique and mem2reg optimization
int main() {
    // Test 1: Simple variable assignments (should generate PHI nodes after mem2reg)
    int x = 10;
    int y = 20;
    int result = x + y;
    
    // Test 2: Conditional assignment (perfect case for PHI nodes)
    int condition = 1;
    int value;
    if (condition > 0) {
        value = 100;
    } else {
        value = 200;
    }
    result = result + value;
    
    // Test 3: Loop with variable updates (should benefit from mem2reg)
    int i = 0;
    int sum = 0;
    while (i < 5) {
        sum = sum + i;
        i = i + 1;
    }
    result = result + sum;
    
    // Test 4: Nested scopes with variable shadowing
    {
        int local_x = 50;
        if (local_x > 0) {
            int nested_y = local_x * 2;
            result = result + nested_y;
        }
    }
    
    // Test 5: Multiple assignments to same variable
    int temp = 1;
    temp = temp * 2;
    temp = temp + 3;
    temp = temp * 4;
    result = result + temp;
    
    return result;
}
