// Test function declarations and calls
// Simple function with no parameters
int simple_func() {
    return 42;
}

// Function with single parameter
int add_one(int x) {
    return x + 1;
}

// Function with multiple parameters
int add_two_numbers(int a, int b) {
    return a + b;
}

// Function with float parameters
float multiply_floats(float x, float y) {
    return x * y;
}

// Function with mixed parameter types
int mixed_params(int a, float b) {
    return a + b;  // Implicit conversion
}

// Void function
void print_number(int n) {
    // In a real implementation, this would print
    // For now, just do some computation
    int temp = n * 2;
}

// Function with array parameter
int sum_array(int arr[], int size) {
    int sum = 0;
    int i = 0;
    while (i < size) {
        sum = sum + arr[i];
        i = i + 1;
    }
    return sum;
}

// Function that calls other functions
int complex_calculation(int x, int y) {
    int result = add_one(x);
    result = add_two_numbers(result, y);
    return result;
}

// Recursive function
int factorial(int n) {
    if (n <= 1) {
        return 1;
    } else {
        return n * factorial(n - 1);
    }
}

// Main function that tests all others
int main() {
    // Test simple function call
    int result1 = simple_func();  // Should return 42
    
    // Test function with single parameter
    int result2 = add_one(10);    // Should return 11
    
    // Test function with multiple parameters
    int result3 = add_two_numbers(5, 7);  // Should return 12
    
    // Test float function
    float result4 = multiply_floats(2.5, 4.0);  // Should return 10.0
    
    // Test mixed parameters
    int result5 = mixed_params(10, 3.5);  // Should return 13
    
    // Test void function call
    print_number(100);
    
    // Test array function
    int test_array[3] = {1, 2, 3};
    int result6 = sum_array(test_array, 3);  // Should return 6
    
    // Test function that calls other functions
    int result7 = complex_calculation(5, 10);  // Should return 16
    
    // Test recursive function
    int result8 = factorial(5);  // Should return 120
    
    // Calculate final result
    int final_result = result1 + result2 + result3 + result5 + result6 + result7 + result8;
    // 42 + 11 + 12 + 13 + 6 + 16 + 120 = 220
    
    return final_result;
}
