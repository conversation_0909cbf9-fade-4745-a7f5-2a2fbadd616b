
// Generated from /home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/grammar/SysY2022.g4 by ANTLR 4.7.2

#pragma once


#include "antlr4-runtime.h"
#include "SysY2022Listener.h"


/**
 * This class provides an empty implementation of SysY2022Listener,
 * which can be extended to create a listener which only needs to handle a subset
 * of the available methods.
 */
class  SysY2022BaseListener : public SysY2022Listener {
public:

  virtual void enterProgram(SysY2022Parser::ProgramContext * /*ctx*/) override { }
  virtual void exitProgram(SysY2022Parser::ProgramContext * /*ctx*/) override { }

  virtual void enterCompUnit(SysY2022Parser::CompUnitContext * /*ctx*/) override { }
  virtual void exitCompUnit(SysY2022Parser::CompUnitContext * /*ctx*/) override { }

  virtual void enterDecl(SysY2022Parser::DeclContext * /*ctx*/) override { }
  virtual void exitDecl(SysY2022Parser::DeclContext * /*ctx*/) override { }

  virtual void enterConstDecl(SysY2022Parser::ConstDeclContext * /*ctx*/) override { }
  virtual void exitConstDecl(SysY2022Parser::ConstDeclContext * /*ctx*/) override { }

  virtual void enterBType(SysY2022Parser::BTypeContext * /*ctx*/) override { }
  virtual void exitBType(SysY2022Parser::BTypeContext * /*ctx*/) override { }

  virtual void enterConstDef(SysY2022Parser::ConstDefContext * /*ctx*/) override { }
  virtual void exitConstDef(SysY2022Parser::ConstDefContext * /*ctx*/) override { }

  virtual void enterConstInitVal(SysY2022Parser::ConstInitValContext * /*ctx*/) override { }
  virtual void exitConstInitVal(SysY2022Parser::ConstInitValContext * /*ctx*/) override { }

  virtual void enterVarDecl(SysY2022Parser::VarDeclContext * /*ctx*/) override { }
  virtual void exitVarDecl(SysY2022Parser::VarDeclContext * /*ctx*/) override { }

  virtual void enterVarDef(SysY2022Parser::VarDefContext * /*ctx*/) override { }
  virtual void exitVarDef(SysY2022Parser::VarDefContext * /*ctx*/) override { }

  virtual void enterInitVal(SysY2022Parser::InitValContext * /*ctx*/) override { }
  virtual void exitInitVal(SysY2022Parser::InitValContext * /*ctx*/) override { }

  virtual void enterFuncDef(SysY2022Parser::FuncDefContext * /*ctx*/) override { }
  virtual void exitFuncDef(SysY2022Parser::FuncDefContext * /*ctx*/) override { }

  virtual void enterFuncType(SysY2022Parser::FuncTypeContext * /*ctx*/) override { }
  virtual void exitFuncType(SysY2022Parser::FuncTypeContext * /*ctx*/) override { }

  virtual void enterFuncFormalParams(SysY2022Parser::FuncFormalParamsContext * /*ctx*/) override { }
  virtual void exitFuncFormalParams(SysY2022Parser::FuncFormalParamsContext * /*ctx*/) override { }

  virtual void enterFuncFormalParam(SysY2022Parser::FuncFormalParamContext * /*ctx*/) override { }
  virtual void exitFuncFormalParam(SysY2022Parser::FuncFormalParamContext * /*ctx*/) override { }

  virtual void enterBlock(SysY2022Parser::BlockContext * /*ctx*/) override { }
  virtual void exitBlock(SysY2022Parser::BlockContext * /*ctx*/) override { }

  virtual void enterBlockItem(SysY2022Parser::BlockItemContext * /*ctx*/) override { }
  virtual void exitBlockItem(SysY2022Parser::BlockItemContext * /*ctx*/) override { }

  virtual void enterStmt(SysY2022Parser::StmtContext * /*ctx*/) override { }
  virtual void exitStmt(SysY2022Parser::StmtContext * /*ctx*/) override { }

  virtual void enterExp(SysY2022Parser::ExpContext * /*ctx*/) override { }
  virtual void exitExp(SysY2022Parser::ExpContext * /*ctx*/) override { }

  virtual void enterCond(SysY2022Parser::CondContext * /*ctx*/) override { }
  virtual void exitCond(SysY2022Parser::CondContext * /*ctx*/) override { }

  virtual void enterLVal(SysY2022Parser::LValContext * /*ctx*/) override { }
  virtual void exitLVal(SysY2022Parser::LValContext * /*ctx*/) override { }

  virtual void enterPrimaryExp(SysY2022Parser::PrimaryExpContext * /*ctx*/) override { }
  virtual void exitPrimaryExp(SysY2022Parser::PrimaryExpContext * /*ctx*/) override { }

  virtual void enterNumber(SysY2022Parser::NumberContext * /*ctx*/) override { }
  virtual void exitNumber(SysY2022Parser::NumberContext * /*ctx*/) override { }

  virtual void enterUnaryExp(SysY2022Parser::UnaryExpContext * /*ctx*/) override { }
  virtual void exitUnaryExp(SysY2022Parser::UnaryExpContext * /*ctx*/) override { }

  virtual void enterUnaryOP(SysY2022Parser::UnaryOPContext * /*ctx*/) override { }
  virtual void exitUnaryOP(SysY2022Parser::UnaryOPContext * /*ctx*/) override { }

  virtual void enterFuncRealParams(SysY2022Parser::FuncRealParamsContext * /*ctx*/) override { }
  virtual void exitFuncRealParams(SysY2022Parser::FuncRealParamsContext * /*ctx*/) override { }

  virtual void enterMulExp(SysY2022Parser::MulExpContext * /*ctx*/) override { }
  virtual void exitMulExp(SysY2022Parser::MulExpContext * /*ctx*/) override { }

  virtual void enterAddExp(SysY2022Parser::AddExpContext * /*ctx*/) override { }
  virtual void exitAddExp(SysY2022Parser::AddExpContext * /*ctx*/) override { }

  virtual void enterRelExp(SysY2022Parser::RelExpContext * /*ctx*/) override { }
  virtual void exitRelExp(SysY2022Parser::RelExpContext * /*ctx*/) override { }

  virtual void enterEqExp(SysY2022Parser::EqExpContext * /*ctx*/) override { }
  virtual void exitEqExp(SysY2022Parser::EqExpContext * /*ctx*/) override { }

  virtual void enterLAndExp(SysY2022Parser::LAndExpContext * /*ctx*/) override { }
  virtual void exitLAndExp(SysY2022Parser::LAndExpContext * /*ctx*/) override { }

  virtual void enterLOrExp(SysY2022Parser::LOrExpContext * /*ctx*/) override { }
  virtual void exitLOrExp(SysY2022Parser::LOrExpContext * /*ctx*/) override { }

  virtual void enterConstExp(SysY2022Parser::ConstExpContext * /*ctx*/) override { }
  virtual void exitConstExp(SysY2022Parser::ConstExpContext * /*ctx*/) override { }


  virtual void enterEveryRule(antlr4::ParserRuleContext * /*ctx*/) override { }
  virtual void exitEveryRule(antlr4::ParserRuleContext * /*ctx*/) override { }
  virtual void visitTerminal(antlr4::tree::TerminalNode * /*node*/) override { }
  virtual void visitErrorNode(antlr4::tree::ErrorNode * /*node*/) override { }

};

