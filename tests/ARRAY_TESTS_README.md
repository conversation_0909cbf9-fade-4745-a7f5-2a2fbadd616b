# Constant Array Test Cases

This directory contains comprehensive test cases for constant array definitions in the SysY2022 compiler.

## Test Files Overview

### 1. `case_array_const_basic.c`
**Purpose**: Test fundamental constant array functionality
- Simple 1D constant arrays
- Partial initialization (`{1, 2, 3}` for larger arrays)
- Empty initialization (`{}`)
- Single element arrays
- Float constant arrays
- Basic array element access

**Key Features Tested**:
- `const int a[5] = {1, 2, 3, 4, 5};`
- `const int b[10] = {1, 2, 3};` (partial init)
- `const int c[3] = {};` (empty init)
- Array element access: `a[0]`, `a[4]`

### 2. `case_array_const_2d.c`
**Purpose**: Test multi-dimensional constant arrays
- 2D arrays with full initialization
- 2D arrays with partial initialization
- 3D arrays (small test case)
- Multi-dimensional array access

**Key Features Tested**:
- `const int matrix[2][3] = {{1, 2, 3}, {4, 5, 6}};`
- `const int partial[3][2] = {{1, 2}, {3}};`
- `const int cube[2][2][2] = {{{1, 2}, {3, 4}}, {{5, 6}, {7, 8}}};`
- Access: `matrix[0][0]`, `cube[1][1][1]`

### 3. `case_array_const_expressions.c`
**Purpose**: Test constant expressions in array definitions
- Array sizes from constant expressions
- Array initialization with constant expressions
- Arithmetic operations in constants
- Nested constant expressions

**Key Features Tested**:
- `const int arr2[2 + 3] = {10, 20, 30, 40, 50};`
- `const int arr3[4] = {1 + 2, 3 * 4, 5 - 1, 6 / 2};`
- `const int arr4[3] = {BASE, BASE + 5, BASE * 2};`

### 4. `case_array_const_global.c`
**Purpose**: Test global vs local constant arrays
- Global constant array definitions
- Mixed global and local constants
- Global constants used in local array sizes
- Function-level constant arrays

**Key Features Tested**:
- Global: `const int global_array[5] = {1, 2, 3, 4, 5};`
- Local: `const int local_array[3] = {10, 20, 30};`
- Mixed access and usage

### 5. `case_array_const_edge_cases.c`
**Purpose**: Test edge cases and boundary conditions
- Large arrays (memory allocation test)
- Arrays with special values (negative, zero, hex, octal)
- Maximum integer values
- Float edge cases
- Performance considerations

**Key Features Tested**:
- `const int large[100] = {1, 2, 3, 4, 5};`
- `const int negative[4] = {-1, -10, -100, -1000};`
- `const int hex_oct[4] = {0xFF, 0x10, 077, 010};`
- `const int big_val[1] = {2147483647};`

### 6. `case_array_const_errors.c`
**Purpose**: Test error handling and edge cases
- Invalid array definitions (commented out to avoid compilation errors)
- Valid cases for comparison
- Error message quality testing

**Key Features Tested**:
- Array size mismatches
- Non-constant array sizes
- Non-constant initializers
- Missing initialization

## Running the Tests

### Quick Test Run
```bash
cd tests
./run_array_tests.sh
```

### Custom Compiler Path
```bash
cd tests
./run_array_tests.sh /path/to/your/compiler
```

### Manual Testing
```bash
cd tests
your_compiler frontend/case_array_const_basic.c
```

## Expected Behavior

### Successful Compilation Should:
1. Parse array syntax correctly
2. Generate appropriate LLVM IR for:
   - Local constant arrays (using `alloca` + `store`)
   - Global constant arrays (using `GlobalVariable`)
   - Scalar constants (direct value storage)
3. Handle array element access
4. Support multi-dimensional arrays
5. Process constant expressions in sizes and initializers

### Generated LLVM IR Examples:

**Local Array**:
```llvm
%a = alloca [5 x i32]
store [5 x i32] [i32 1, i32 2, i32 3, i32 4, i32 5], [5 x i32]* %a
```

**Global Array**:
```llvm
@global_array = internal constant [5 x i32] [i32 1, i32 2, i32 3, i32 4, i32 5]
```

**Array Access**:
```llvm
%arrayidx = getelementptr inbounds [5 x i32], [5 x i32]* %a, i64 0, i64 0
%0 = load i32, i32* %arrayidx
```

## Testing the `visitConstDef` Implementation

These test cases specifically validate:

1. **Array dimension calculation** - extracting sizes from `constExp`
2. **Array type construction** - building `ArrayType` from dimensions
3. **Initialization processing** - handling `{1, 2, 3}` syntax
4. **Context-aware storage** - global vs local constant decisions
5. **Symbol table management** - proper storage in `named_values`
6. **LLVM IR generation** - correct `CreateAlloca` and `GlobalVariable` usage

## Debugging Tips

If tests fail, check:
1. **Parser issues**: Does the grammar correctly parse array syntax?
2. **Type issues**: Are array types constructed correctly?
3. **Initialization issues**: Is `visitConstInitVal` handling `{}` syntax?
4. **Memory issues**: Are `CreateAlloca` calls using correct parameters?
5. **Symbol table issues**: Are constants stored and retrieved correctly?

The test results will be saved in `tests/results/` for detailed analysis.
