// Test arrays and constants in blocks
int main() {
    int global_var = 10;
    
    // Test 1: Arrays in blocks
    {
        int local_array[5] = {1, 2, 3, 4, 5};
        float float_array[3] = {1.1, 2.2, 3.3};
        
        // Access array elements
        int sum = local_array[0] + local_array[4];
        global_var = global_var + sum;
    }
    // local_array and float_array not accessible here
    
    // Test 2: Constants in blocks
    {
        const int BLOCK_CONSTANT = 42;
        const float PI = 3.14;
        const int const_array[3] = {10, 20, 30};
        
        global_var = global_var + BLOCK_CONSTANT;
        global_var = global_var + const_array[1];
    }
    // Constants not accessible here
    
    // Test 3: Mixed declarations in blocks
    {
        int a = 1, b[3] = {2, 3, 4}, c = 5;
        const int d = 6, e[2] = {7, 8};
        float f = 1.5, g[2] = {2.5, 3.5};
        
        int result = a + b[1] + c + d + e[0];
        global_var = global_var + result;
    }
    
    // Test 4: Shadowing with arrays
    {
        int global_var[3] = {100, 200, 300};  // Shadows outer global_var
        int local_sum = global_var[0] + global_var[2];
    }
    // global_var is back to the original scalar variable
    
    // Test 5: Control flow with block-scoped arrays
    if (global_var > 0) {
        int if_array[2] = {global_var, global_var * 2};
        global_var = if_array[1];
    } else {
        int else_array[2] = {-global_var, -global_var * 2};
        global_var = else_array[1];
    }
    
    // Test 6: While loop with block-scoped variables
    int counter = 0;
    while (counter < 2) {
        int loop_array[3] = {counter, counter + 1, counter + 2};
        const int LOOP_CONST = counter * 10;
        
        global_var = global_var + loop_array[2] + LOOP_CONST;
        counter = counter + 1;
    }
    
    return global_var;
}
