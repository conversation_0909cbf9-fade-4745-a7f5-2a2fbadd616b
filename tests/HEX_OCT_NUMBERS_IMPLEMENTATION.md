# Hexadecimal and Octal Numbers Implementation

## 🎯 **Problem Identified and Solved**

The original `visitNumber` function only handled decimal integers and floats using `std::stoi(text)`, which defaults to base 10. This meant:
- ❌ Hexadecimal numbers like `0xFF` were incorrectly parsed
- ❌ Octal numbers like `077` were incorrectly parsed
- ❌ Only decimal numbers worked correctly

## 🔍 **Grammar Analysis**

The SysY2022 grammar already supports all number formats:

**From `SysY2022Lex.g4`:**
```antlr
// Number format definitions
fragment OCTPREFIX      : '0';
fragment OCTDIGIT       : [0-7];
fragment HEXPREFIX      : '0' [xX];
fragment HEXDIGIT       : [0-9a-fA-F];

fragment DECCONST       : ZERO | NONZERODIGIT DIGIT*;
fragment OCTCONST       : OCTPREFIX OCTDIGIT+;
fragment HEXCONST       : HEXPREFIX HEXDIGIT+;

INTCONST    : DECCONST | OCTCONST | HEXCONST;
```

**Supported Formats:**
- **Decimal**: `123`, `456`, `0` (regular numbers)
- **Hexadecimal**: `0x123`, `0X123`, `0xff`, `0XFF` (0x/0X prefix)
- **Octal**: `0123`, `077`, `010` (0 prefix followed by octal digits)

## 🔧 **Complete Implementation**

### **Before (Broken):**
```cpp
if (ctx->INTCONST()) {
    // Integer constant
    int value = std::stoi(ctx->INTCONST()->getText());  // ❌ Only decimal!
    llvm_number = llvm::ConstantInt::get(llvm::Type::getInt32Ty(*llvm_context), value);
    std::cout << "Number (Int): " << value << std::endl;
}
```

### **After (Complete):**
```cpp
if (ctx->INTCONST()) {
    // Integer constant - handle decimal, hexadecimal, and octal
    std::string text = ctx->INTCONST()->getText();
    int value = 0;
    
    if (text.length() >= 2 && (text.substr(0, 2) == "0x" || text.substr(0, 2) == "0X")) {
        // Hexadecimal: 0x... or 0X...
        value = std::stoi(text, nullptr, 16);
        std::cout << "Number (Hex): " << text << " = " << value << std::endl;
    } else if (text.length() >= 2 && text[0] == '0' && text[1] >= '0' && text[1] <= '7') {
        // Octal: 0... (starts with 0 and followed by octal digits)
        value = std::stoi(text, nullptr, 8);
        std::cout << "Number (Oct): " << text << " = " << value << std::endl;
    } else {
        // Decimal: regular number
        value = std::stoi(text, nullptr, 10);
        std::cout << "Number (Dec): " << value << std::endl;
    }
    
    llvm_number = llvm::ConstantInt::get(llvm::Type::getInt32Ty(*llvm_context), value);
}
```

## 🎯 **Key Features Implemented**

### ✅ **1. Hexadecimal Number Support**
- **Format**: `0x` or `0X` prefix followed by hex digits (0-9, a-f, A-F)
- **Examples**: `0x10` (16), `0xFF` (255), `0XaBc` (2748)
- **Case insensitive**: Both `0xff` and `0xFF` work
- **Mixed case**: `0xAbC` works correctly

### ✅ **2. Octal Number Support**
- **Format**: `0` prefix followed by octal digits (0-7)
- **Examples**: `010` (8), `077` (63), `0123` (83)
- **Proper detection**: Distinguishes from decimal zero

### ✅ **3. Decimal Number Support**
- **Format**: Regular decimal numbers
- **Examples**: `123`, `456`, `0`
- **Unchanged**: Existing decimal support preserved

### ✅ **4. Automatic Base Detection**
- **Hexadecimal**: Detected by `0x` or `0X` prefix
- **Octal**: Detected by `0` prefix + octal digits
- **Decimal**: Default for all other cases

### ✅ **5. Proper Parsing**
- Uses `std::stoi(text, nullptr, base)` with correct base
- **Base 16** for hexadecimal
- **Base 8** for octal  
- **Base 10** for decimal

## 🚀 **Generated LLVM IR Examples**

### **Hexadecimal Numbers:**
```llvm
; 0xFF = 255
%1 = alloca i32, align 4
store i32 255, i32* %1, align 4

; 0x10 = 16
%2 = alloca i32, align 4
store i32 16, i32* %2, align 4
```

### **Octal Numbers:**
```llvm
; 077 = 63
%3 = alloca i32, align 4
store i32 63, i32* %3, align 4

; 010 = 8
%4 = alloca i32, align 4
store i32 8, i32* %4, align 4
```

### **Mixed Arithmetic:**
```llvm
; 0x10 + 010 + 10 = 16 + 8 + 10 = 34
%5 = add i32 16, 8
%6 = add i32 %5, 10
```

## 🧪 **Comprehensive Test Coverage**

### 1. **`case_hex_oct_numbers_test.c`**
- Hexadecimal numbers (lowercase and uppercase)
- Octal numbers
- Decimal numbers for comparison
- Zero in different formats
- Large numbers
- Use in expressions and arrays
- Constants with different formats

### 2. **`case_number_formats_edge_cases_test.c`**
- Minimum and maximum values
- Common hex/octal values
- Powers of 2 in different formats
- All hex digits (A-F, both cases)
- All octal digits (0-7)
- Mixed format arithmetic
- Use in control flow and loops
- Array indexing with different formats

### 3. **`case_simple_hex_oct_test.c`**
- Basic functionality verification
- Simple arithmetic with mixed formats

## 📋 **Supported Number Examples**

| Format | Example | Decimal Value | Description |
|--------|---------|---------------|-------------|
| **Decimal** | `123` | 123 | Regular decimal |
| **Decimal** | `0` | 0 | Decimal zero |
| **Hexadecimal** | `0x10` | 16 | Lowercase x |
| **Hexadecimal** | `0X10` | 16 | Uppercase X |
| **Hexadecimal** | `0xff` | 255 | Lowercase hex digits |
| **Hexadecimal** | `0XFF` | 255 | Uppercase hex digits |
| **Hexadecimal** | `0xAbC` | 2748 | Mixed case |
| **Octal** | `010` | 8 | Basic octal |
| **Octal** | `077` | 63 | Max 2-digit octal |
| **Octal** | `0123` | 83 | Multi-digit octal |

## ✅ **Verification Status**

| Feature | Status | Test Coverage |
|---------|--------|---------------|
| Decimal numbers | ✅ Working | ✅ Tested |
| Hexadecimal (0x) | ✅ Working | ✅ Tested |
| Hexadecimal (0X) | ✅ Working | ✅ Tested |
| Hex case insensitive | ✅ Working | ✅ Tested |
| Octal numbers | ✅ Working | ✅ Tested |
| Zero in all formats | ✅ Working | ✅ Tested |
| Mixed format arithmetic | ✅ Working | ✅ Tested |
| Array sizes/indexing | ✅ Working | ✅ Tested |
| Constants | ✅ Working | ✅ Tested |
| Control flow | ✅ Working | ✅ Tested |

## 🎉 **Result**

The `visitNumber` function now correctly handles all number formats:
- ✅ **Hexadecimal**: `0x10`, `0XFF`, `0xAbC` → Proper base-16 parsing
- ✅ **Octal**: `010`, `077`, `0123` → Proper base-8 parsing  
- ✅ **Decimal**: `123`, `456`, `0` → Proper base-10 parsing
- ✅ **Automatic detection**: Correct base selection based on prefix
- ✅ **Case insensitive**: Both `0xff` and `0xFF` work
- ✅ **LLVM IR generation**: Correct constant values in IR
- ✅ **Full integration**: Works in expressions, arrays, constants, control flow

The implementation now supports the complete C-style number format specification!
