[@0,26:28='int',<1>,2:0]
[@1,30:33='main',<34>,2:4]
[@2,34:34='(',<13>,2:8]
[@3,35:35=')',<14>,2:9]
[@4,36:36='{',<15>,2:10]
[@5,42:44='int',<1>,3:4]
[@6,46:46='a',<34>,3:8]
[@7,47:47=',',<12>,3:9]
[@8,49:49='b',<34>,3:11]
[@9,50:50=';',<11>,3:12]
[@10,56:56='a',<34>,4:4]
[@11,58:58='=',<19>,4:6]
[@12,60:62='0xf',<38>,4:8]
[@13,63:63=';',<11>,4:11]
[@14,69:69='b',<34>,5:4]
[@15,71:71='=',<19>,5:6]
[@16,73:75='0xc',<38>,5:8]
[@17,76:76=';',<11>,5:11]
[@18,82:87='return',<10>,6:4]
[@19,89:89='a',<34>,6:11]
[@20,91:91='+',<20>,6:13]
[@21,93:93='b',<34>,6:15]
[@22,95:95='+',<20>,6:17]
[@23,97:99='075',<38>,6:19]
[@24,100:100=';',<11>,6:22]
[@25,102:102='}',<16>,7:0]
[@26,104:103='<EOF>',<-1>,8:0]
(program (compUnit (funcDef (funcType (bType int)) main ( ) (block { (blockItem (decl (varDecl (bType int) (varDef a) , (varDef b) ;))) (blockItem (stmt (lVal a) = (exp (addExp (mulExp (unaryExp (primaryExp (number 0xf)))))) ;)) (blockItem (stmt (lVal b) = (exp (addExp (mulExp (unaryExp (primaryExp (number 0xc)))))) ;)) (blockItem (stmt return (exp (addExp (addExp (addExp (mulExp (unaryExp (primaryExp (lVal a))))) + (mulExp (unaryExp (primaryExp (lVal b))))) + (mulExp (unaryExp (primaryExp (number 075)))))) ;)) }))) <EOF>)

=== AST Structure ===
Program (
  CompUnit (
    FuncDef: main (
      FuncResultType: int
      FuncFormalParams: (void)
      Block (
        Using BasicBlock: entry
        Var Decl
          VarDecl (
            Variable: a (Scalar)
            Variable: b (Scalar)
          ) End VarDecl
        Assignment Stmt (
          Expression (
            AddExp (
              MulExp (
                UnaryExp (
                  PrimaryExp (
                    Number (Hex): 0xf = 15
                  ) End PrimaryExp
                ) End UnaryExp
              ) End MulExp
            ) End AddExp
          ) End Expression
          Storing value to variable: a
        ) End Assignment
        Assignment Stmt (
          Expression (
            AddExp (
              MulExp (
                UnaryExp (
                  PrimaryExp (
                    Number (Hex): 0xc = 12
                  ) End PrimaryExp
                ) End UnaryExp
              ) End MulExp
            ) End AddExp
          ) End Expression
          Storing value to variable: b
        ) End Assignment
        Return Stmt (
          Expression (
            AddExp (
              AddExp (
                AddExp (
                  MulExp (
                    UnaryExp (
                      PrimaryExp (
                        LVal: a
                      ) End PrimaryExp
                    ) End UnaryExp
                  ) End MulExp
                ) End AddExp
                MulExp (
                  UnaryExp (
                    PrimaryExp (
                      LVal: b
                    ) End PrimaryExp
                  ) End UnaryExp
                ) End MulExp
              ) End AddExp
              MulExp (
                UnaryExp (
                  PrimaryExp (
                    Number (Oct): 075 = 61
                  ) End PrimaryExp
                ) End UnaryExp
              ) End MulExp
            ) End AddExp
          ) End Expression
        ) End Return
      ) End Block
    ) End FuncDef
  ) End CompUnit
) End Program

=== Unoptimized LLVM IR ===
; ModuleID = 'SysY2022'
source_filename = "SysY2022"

define i32 @main() {
entry:
  %a = alloca i32, align 4
  %b = alloca i32, align 4
  store i32 15, i32* %a, align 4
  store i32 12, i32* %b, align 4
  %a1 = load i32, i32* %a, align 4
  %b2 = load i32, i32* %b, align 4
  %addtmp = add i32 %a1, %b2
  %addtmp3 = add i32 %addtmp, 61
  ret i32 %addtmp3
}

=== Optimized LLVM IR (after mem2reg) ===
; ModuleID = 'SysY2022'
source_filename = "SysY2022"

define i32 @main() {
entry:
  %a = alloca i32, align 4
  %b = alloca i32, align 4
  store i32 15, i32* %a, align 4
  store i32 12, i32* %b, align 4
  %a1 = load i32, i32* %a, align 4
  %b2 = load i32, i32* %b, align 4
  %addtmp = add i32 %a1, %b2
  %addtmp3 = add i32 %addtmp, 61
  ret i32 %addtmp3
}
