// Test block scoping and variable shadowing
int main() {
    int x = 1;
    int y = 2;
    
    // Test 1: Simple nested block
    {
        int z = 3;          // Local to this block
        x = x + z;          // Access outer variable
        int w = x + y;      // Use both outer and local variables
    }
    // z and w are no longer accessible here
    
    // Test 2: Variable shadowing
    {
        int x = 10;         // Shadows outer x
        int local = x + y;  // Uses shadowed x (10) and outer y (2)
    }
    // x should be back to its original value (modified in first block)
    
    // Test 3: Nested blocks
    {
        int a = 5;
        {
            int b = 6;
            {
                int c = 7;
                int sum = a + b + c;  // Access all three levels
            }
            // c no longer accessible
            int sum2 = a + b;
        }
        // b and c no longer accessible
        int sum3 = a;
    }
    // a, b, c no longer accessible
    
    // Test 4: If statement with blocks
    if (x > 0) {
        int positive = 1;
        y = y + positive;
    } else {
        int negative = -1;
        y = y + negative;
    }
    // positive and negative not accessible here
    
    // Test 5: While loop with block
    int i = 0;
    while (i < 3) {
        int loop_var = i * 2;
        i = i + 1;
    }
    // loop_var not accessible here
    
    return x + y;
}
