lexer grammar SysY2022Lex;

/// Terminator

// BType
INT         : 'int';
FLOAT       : 'float';
// FuncType + INT & FLOAT
VOID        : 'void';

// Type modifier
CONST       : 'const';

// Branch
IF          : 'if';
ELSE        : 'else';
WHILE       : 'while';
BREAK       : 'break';
CONTINUE    : 'continue';
RETURN      : 'return';

// delimenator
SEMICOLON   : ';';
COMMA       : ',';
LPAREN      : '(';
RPAREN      : ')';
LBRACE      : '{';
RBRACE      : '}';
LBRACKET    : '[';
RBRACKET    : ']';

// operator
ASSIGN      : '=';
ADD         : '+';
SUB         : '-';
MUL         : '*';
DIV         : '/';
MOD         : '%';
LT          : '<';
GT          : '>';
LE          : '<=';
GE          : '>=';
EQ          : '==';
NE          : '!=';
AND         : '&&';
OR          : '||';
NOT         : '!';


fragment DIGIT          : [0-9];
fragment NONZERODIGIT   : [1-9];
fragment ZERO           : '0';
fragment NONDIGIT       : [a-zA-Z_];
fragment ALPHANUM       : [a-zA-Z0-9_];
// Decimal
fragment DECDIGIT       : [0-9];
fragment OCTPREFIX      : '0';
fragment OCTDIGIT       : [0-7];
fragment HEXPREFIX      : '0' [xX];
fragment HEXDIGIT       : [0-9a-fA-F];

// TODO: underscore?
IDENT       : NONDIGIT ALPHANUM*;

/// COMMENT
WHIESPACE           : [ \t\r\n] -> skip;
// LINECOMMENT         : '//' (.*? | '\r'?)* '\n'        -> skip;
// BLOCKCOMMENT        : '/*' (.*? | '\r'? | '\n'?)* '*/'   -> skip;
LINECOMMENT         : '//' .*? '\r'? '\n'   -> skip;
BLOCKCOMMENT        : '/*' .*? '*/'         -> skip;

/// CONST
fragment DECCONST       : ZERO | NONZERODIGIT DIGIT*;
// fragment OCTCONST       : OCTPREFIX | OCTCONST OCTDIGIT;
fragment OCTCONST       : OCTPREFIX OCTDIGIT+;
fragment HEXCONST       : HEXPREFIX HEXDIGIT+;

// fragment BINARY      : '0' [bB] [01]+;

INTCONST    : DECCONST | OCTCONST | HEXCONST;
FLOATCONST  : DECCONST? '.' DECCONST+; // TODO: not good definition





