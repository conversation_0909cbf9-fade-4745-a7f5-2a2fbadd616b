// Generated from /home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022.g4 by ANTLR 4.13.1
import org.antlr.v4.runtime.Lexer;
import org.antlr.v4.runtime.CharStream;
import org.antlr.v4.runtime.Token;
import org.antlr.v4.runtime.TokenStream;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.atn.*;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.misc.*;

@SuppressWarnings({"all", "warnings", "unchecked", "unused", "cast", "CheckReturnValue", "this-escape"})
public class SysY2022Lexer extends Lexer {
	static { RuntimeMetaData.checkVersion("4.13.1", RuntimeMetaData.VERSION); }

	protected static final DFA[] _decisionToDFA;
	protected static final PredictionContextCache _sharedContextCache =
		new PredictionContextCache();
	public static final int
		INT=1, FLOAT=2, VOID=3, CONST=4, IF=5, ELSE=6, WHILE=7, BREAK=8, CONTINUE=9, 
		RETURN=10, SEMICOLON=11, COMMA=12, LPAREN=13, RPAREN=14, LBRACE=15, RBRACE=16, 
		LBRACKET=17, RBRACKET=18, ASSIGN=19, ADD=20, SUB=21, MUL=22, DIV=23, MOD=24, 
		LT=25, GT=26, LE=27, GE=28, EQ=29, NE=30, AND=31, OR=32, NOT=33, IDENT=34, 
		WHIESPACE=35, LINECOMMENT=36, BLOCKCOMMENT=37, INTCONST=38, FLOATCONST=39;
	public static String[] channelNames = {
		"DEFAULT_TOKEN_CHANNEL", "HIDDEN"
	};

	public static String[] modeNames = {
		"DEFAULT_MODE"
	};

	private static String[] makeRuleNames() {
		return new String[] {
			"INT", "FLOAT", "VOID", "CONST", "IF", "ELSE", "WHILE", "BREAK", "CONTINUE", 
			"RETURN", "SEMICOLON", "COMMA", "LPAREN", "RPAREN", "LBRACE", "RBRACE", 
			"LBRACKET", "RBRACKET", "ASSIGN", "ADD", "SUB", "MUL", "DIV", "MOD", 
			"LT", "GT", "LE", "GE", "EQ", "NE", "AND", "OR", "NOT", "DIGIT", "NONZERODIGIT", 
			"NONDIGIT", "ALPHANUM", "DECDIGIT", "OCTPREFIX", "OCTDIGIT", "HEXPREFIX", 
			"HEXDIGIT", "IDENT", "WHIESPACE", "LINECOMMENT", "BLOCKCOMMENT", "DECCONST", 
			"OCTCONST", "HEXCONST", "INTCONST", "FLOATCONST"
		};
	}
	public static final String[] ruleNames = makeRuleNames();

	private static String[] makeLiteralNames() {
		return new String[] {
			null, "'int'", "'float'", "'void'", "'const'", "'if'", "'else'", "'while'", 
			"'break'", "'continue'", "'return'", "';'", "','", "'('", "')'", "'{'", 
			"'}'", "'['", "']'", "'='", "'+'", "'-'", "'*'", "'/'", "'%'", "'<'", 
			"'>'", "'<='", "'>='", "'=='", "'!='", "'&&'", "'||'", "'!'"
		};
	}
	private static final String[] _LITERAL_NAMES = makeLiteralNames();
	private static String[] makeSymbolicNames() {
		return new String[] {
			null, "INT", "FLOAT", "VOID", "CONST", "IF", "ELSE", "WHILE", "BREAK", 
			"CONTINUE", "RETURN", "SEMICOLON", "COMMA", "LPAREN", "RPAREN", "LBRACE", 
			"RBRACE", "LBRACKET", "RBRACKET", "ASSIGN", "ADD", "SUB", "MUL", "DIV", 
			"MOD", "LT", "GT", "LE", "GE", "EQ", "NE", "AND", "OR", "NOT", "IDENT", 
			"WHIESPACE", "LINECOMMENT", "BLOCKCOMMENT", "INTCONST", "FLOATCONST"
		};
	}
	private static final String[] _SYMBOLIC_NAMES = makeSymbolicNames();
	public static final Vocabulary VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);

	/**
	 * @deprecated Use {@link #VOCABULARY} instead.
	 */
	@Deprecated
	public static final String[] tokenNames;
	static {
		tokenNames = new String[_SYMBOLIC_NAMES.length];
		for (int i = 0; i < tokenNames.length; i++) {
			tokenNames[i] = VOCABULARY.getLiteralName(i);
			if (tokenNames[i] == null) {
				tokenNames[i] = VOCABULARY.getSymbolicName(i);
			}

			if (tokenNames[i] == null) {
				tokenNames[i] = "<INVALID>";
			}
		}
	}

	@Override
	@Deprecated
	public String[] getTokenNames() {
		return tokenNames;
	}

	@Override

	public Vocabulary getVocabulary() {
		return VOCABULARY;
	}


	public SysY2022Lexer(CharStream input) {
		super(input);
		_interp = new LexerATNSimulator(this,_ATN,_decisionToDFA,_sharedContextCache);
	}

	@Override
	public String getGrammarFileName() { return "SysY2022.g4"; }

	@Override
	public String[] getRuleNames() { return ruleNames; }

	@Override
	public String getSerializedATN() { return _serializedATN; }

	@Override
	public String[] getChannelNames() { return channelNames; }

	@Override
	public String[] getModeNames() { return modeNames; }

	@Override
	public ATN getATN() { return _ATN; }

	public static final String _serializedATN =
		"\u0004\u0000\'\u0131\u0006\uffff\uffff\u0002\u0000\u0007\u0000\u0002\u0001"+
		"\u0007\u0001\u0002\u0002\u0007\u0002\u0002\u0003\u0007\u0003\u0002\u0004"+
		"\u0007\u0004\u0002\u0005\u0007\u0005\u0002\u0006\u0007\u0006\u0002\u0007"+
		"\u0007\u0007\u0002\b\u0007\b\u0002\t\u0007\t\u0002\n\u0007\n\u0002\u000b"+
		"\u0007\u000b\u0002\f\u0007\f\u0002\r\u0007\r\u0002\u000e\u0007\u000e\u0002"+
		"\u000f\u0007\u000f\u0002\u0010\u0007\u0010\u0002\u0011\u0007\u0011\u0002"+
		"\u0012\u0007\u0012\u0002\u0013\u0007\u0013\u0002\u0014\u0007\u0014\u0002"+
		"\u0015\u0007\u0015\u0002\u0016\u0007\u0016\u0002\u0017\u0007\u0017\u0002"+
		"\u0018\u0007\u0018\u0002\u0019\u0007\u0019\u0002\u001a\u0007\u001a\u0002"+
		"\u001b\u0007\u001b\u0002\u001c\u0007\u001c\u0002\u001d\u0007\u001d\u0002"+
		"\u001e\u0007\u001e\u0002\u001f\u0007\u001f\u0002 \u0007 \u0002!\u0007"+
		"!\u0002\"\u0007\"\u0002#\u0007#\u0002$\u0007$\u0002%\u0007%\u0002&\u0007"+
		"&\u0002\'\u0007\'\u0002(\u0007(\u0002)\u0007)\u0002*\u0007*\u0002+\u0007"+
		"+\u0002,\u0007,\u0002-\u0007-\u0002.\u0007.\u0002/\u0007/\u00020\u0007"+
		"0\u00021\u00071\u00022\u00072\u0001\u0000\u0001\u0000\u0001\u0000\u0001"+
		"\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001"+
		"\u0001\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001"+
		"\u0003\u0001\u0003\u0001\u0003\u0001\u0003\u0001\u0003\u0001\u0003\u0001"+
		"\u0004\u0001\u0004\u0001\u0004\u0001\u0005\u0001\u0005\u0001\u0005\u0001"+
		"\u0005\u0001\u0005\u0001\u0006\u0001\u0006\u0001\u0006\u0001\u0006\u0001"+
		"\u0006\u0001\u0006\u0001\u0007\u0001\u0007\u0001\u0007\u0001\u0007\u0001"+
		"\u0007\u0001\u0007\u0001\b\u0001\b\u0001\b\u0001\b\u0001\b\u0001\b\u0001"+
		"\b\u0001\b\u0001\b\u0001\t\u0001\t\u0001\t\u0001\t\u0001\t\u0001\t\u0001"+
		"\t\u0001\n\u0001\n\u0001\u000b\u0001\u000b\u0001\f\u0001\f\u0001\r\u0001"+
		"\r\u0001\u000e\u0001\u000e\u0001\u000f\u0001\u000f\u0001\u0010\u0001\u0010"+
		"\u0001\u0011\u0001\u0011\u0001\u0012\u0001\u0012\u0001\u0013\u0001\u0013"+
		"\u0001\u0014\u0001\u0014\u0001\u0015\u0001\u0015\u0001\u0016\u0001\u0016"+
		"\u0001\u0017\u0001\u0017\u0001\u0018\u0001\u0018\u0001\u0019\u0001\u0019"+
		"\u0001\u001a\u0001\u001a\u0001\u001a\u0001\u001b\u0001\u001b\u0001\u001b"+
		"\u0001\u001c\u0001\u001c\u0001\u001c\u0001\u001d\u0001\u001d\u0001\u001d"+
		"\u0001\u001e\u0001\u001e\u0001\u001e\u0001\u001f\u0001\u001f\u0001\u001f"+
		"\u0001 \u0001 \u0001!\u0001!\u0001\"\u0001\"\u0001#\u0001#\u0001$\u0001"+
		"$\u0001%\u0001%\u0001&\u0001&\u0001\'\u0001\'\u0001(\u0001(\u0001(\u0001"+
		")\u0001)\u0001*\u0001*\u0005*\u00ea\b*\n*\f*\u00ed\t*\u0001+\u0001+\u0001"+
		"+\u0001+\u0001,\u0001,\u0001,\u0001,\u0005,\u00f7\b,\n,\f,\u00fa\t,\u0001"+
		",\u0003,\u00fd\b,\u0001,\u0001,\u0001,\u0001,\u0001-\u0001-\u0001-\u0001"+
		"-\u0005-\u0107\b-\n-\f-\u010a\t-\u0001-\u0001-\u0001-\u0001-\u0001-\u0001"+
		".\u0001.\u0005.\u0113\b.\n.\f.\u0116\t.\u0001/\u0001/\u0004/\u011a\b/"+
		"\u000b/\f/\u011b\u00010\u00010\u00040\u0120\b0\u000b0\f0\u0121\u00011"+
		"\u00011\u00011\u00031\u0127\b1\u00012\u00032\u012a\b2\u00012\u00012\u0004"+
		"2\u012e\b2\u000b2\f2\u012f\u0002\u00f8\u0108\u00003\u0001\u0001\u0003"+
		"\u0002\u0005\u0003\u0007\u0004\t\u0005\u000b\u0006\r\u0007\u000f\b\u0011"+
		"\t\u0013\n\u0015\u000b\u0017\f\u0019\r\u001b\u000e\u001d\u000f\u001f\u0010"+
		"!\u0011#\u0012%\u0013\'\u0014)\u0015+\u0016-\u0017/\u00181\u00193\u001a"+
		"5\u001b7\u001c9\u001d;\u001e=\u001f? A!C\u0000E\u0000G\u0000I\u0000K\u0000"+
		"M\u0000O\u0000Q\u0000S\u0000U\"W#Y$[%]\u0000_\u0000a\u0000c&e\'\u0001"+
		"\u0000\b\u0001\u000009\u0001\u000019\u0002\u0000AZaz\u0003\u000009AZa"+
		"z\u0001\u000007\u0002\u0000XXxx\u0003\u000009AFaf\u0003\u0000\t\n\r\r"+
		"  \u012f\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0003\u0001\u0000\u0000"+
		"\u0000\u0000\u0005\u0001\u0000\u0000\u0000\u0000\u0007\u0001\u0000\u0000"+
		"\u0000\u0000\t\u0001\u0000\u0000\u0000\u0000\u000b\u0001\u0000\u0000\u0000"+
		"\u0000\r\u0001\u0000\u0000\u0000\u0000\u000f\u0001\u0000\u0000\u0000\u0000"+
		"\u0011\u0001\u0000\u0000\u0000\u0000\u0013\u0001\u0000\u0000\u0000\u0000"+
		"\u0015\u0001\u0000\u0000\u0000\u0000\u0017\u0001\u0000\u0000\u0000\u0000"+
		"\u0019\u0001\u0000\u0000\u0000\u0000\u001b\u0001\u0000\u0000\u0000\u0000"+
		"\u001d\u0001\u0000\u0000\u0000\u0000\u001f\u0001\u0000\u0000\u0000\u0000"+
		"!\u0001\u0000\u0000\u0000\u0000#\u0001\u0000\u0000\u0000\u0000%\u0001"+
		"\u0000\u0000\u0000\u0000\'\u0001\u0000\u0000\u0000\u0000)\u0001\u0000"+
		"\u0000\u0000\u0000+\u0001\u0000\u0000\u0000\u0000-\u0001\u0000\u0000\u0000"+
		"\u0000/\u0001\u0000\u0000\u0000\u00001\u0001\u0000\u0000\u0000\u00003"+
		"\u0001\u0000\u0000\u0000\u00005\u0001\u0000\u0000\u0000\u00007\u0001\u0000"+
		"\u0000\u0000\u00009\u0001\u0000\u0000\u0000\u0000;\u0001\u0000\u0000\u0000"+
		"\u0000=\u0001\u0000\u0000\u0000\u0000?\u0001\u0000\u0000\u0000\u0000A"+
		"\u0001\u0000\u0000\u0000\u0000U\u0001\u0000\u0000\u0000\u0000W\u0001\u0000"+
		"\u0000\u0000\u0000Y\u0001\u0000\u0000\u0000\u0000[\u0001\u0000\u0000\u0000"+
		"\u0000c\u0001\u0000\u0000\u0000\u0000e\u0001\u0000\u0000\u0000\u0001g"+
		"\u0001\u0000\u0000\u0000\u0003k\u0001\u0000\u0000\u0000\u0005q\u0001\u0000"+
		"\u0000\u0000\u0007v\u0001\u0000\u0000\u0000\t|\u0001\u0000\u0000\u0000"+
		"\u000b\u007f\u0001\u0000\u0000\u0000\r\u0084\u0001\u0000\u0000\u0000\u000f"+
		"\u008a\u0001\u0000\u0000\u0000\u0011\u0090\u0001\u0000\u0000\u0000\u0013"+
		"\u0099\u0001\u0000\u0000\u0000\u0015\u00a0\u0001\u0000\u0000\u0000\u0017"+
		"\u00a2\u0001\u0000\u0000\u0000\u0019\u00a4\u0001\u0000\u0000\u0000\u001b"+
		"\u00a6\u0001\u0000\u0000\u0000\u001d\u00a8\u0001\u0000\u0000\u0000\u001f"+
		"\u00aa\u0001\u0000\u0000\u0000!\u00ac\u0001\u0000\u0000\u0000#\u00ae\u0001"+
		"\u0000\u0000\u0000%\u00b0\u0001\u0000\u0000\u0000\'\u00b2\u0001\u0000"+
		"\u0000\u0000)\u00b4\u0001\u0000\u0000\u0000+\u00b6\u0001\u0000\u0000\u0000"+
		"-\u00b8\u0001\u0000\u0000\u0000/\u00ba\u0001\u0000\u0000\u00001\u00bc"+
		"\u0001\u0000\u0000\u00003\u00be\u0001\u0000\u0000\u00005\u00c0\u0001\u0000"+
		"\u0000\u00007\u00c3\u0001\u0000\u0000\u00009\u00c6\u0001\u0000\u0000\u0000"+
		";\u00c9\u0001\u0000\u0000\u0000=\u00cc\u0001\u0000\u0000\u0000?\u00cf"+
		"\u0001\u0000\u0000\u0000A\u00d2\u0001\u0000\u0000\u0000C\u00d4\u0001\u0000"+
		"\u0000\u0000E\u00d6\u0001\u0000\u0000\u0000G\u00d8\u0001\u0000\u0000\u0000"+
		"I\u00da\u0001\u0000\u0000\u0000K\u00dc\u0001\u0000\u0000\u0000M\u00de"+
		"\u0001\u0000\u0000\u0000O\u00e0\u0001\u0000\u0000\u0000Q\u00e2\u0001\u0000"+
		"\u0000\u0000S\u00e5\u0001\u0000\u0000\u0000U\u00e7\u0001\u0000\u0000\u0000"+
		"W\u00ee\u0001\u0000\u0000\u0000Y\u00f2\u0001\u0000\u0000\u0000[\u0102"+
		"\u0001\u0000\u0000\u0000]\u0110\u0001\u0000\u0000\u0000_\u0117\u0001\u0000"+
		"\u0000\u0000a\u011d\u0001\u0000\u0000\u0000c\u0126\u0001\u0000\u0000\u0000"+
		"e\u0129\u0001\u0000\u0000\u0000gh\u0005i\u0000\u0000hi\u0005n\u0000\u0000"+
		"ij\u0005t\u0000\u0000j\u0002\u0001\u0000\u0000\u0000kl\u0005f\u0000\u0000"+
		"lm\u0005l\u0000\u0000mn\u0005o\u0000\u0000no\u0005a\u0000\u0000op\u0005"+
		"t\u0000\u0000p\u0004\u0001\u0000\u0000\u0000qr\u0005v\u0000\u0000rs\u0005"+
		"o\u0000\u0000st\u0005i\u0000\u0000tu\u0005d\u0000\u0000u\u0006\u0001\u0000"+
		"\u0000\u0000vw\u0005c\u0000\u0000wx\u0005o\u0000\u0000xy\u0005n\u0000"+
		"\u0000yz\u0005s\u0000\u0000z{\u0005t\u0000\u0000{\b\u0001\u0000\u0000"+
		"\u0000|}\u0005i\u0000\u0000}~\u0005f\u0000\u0000~\n\u0001\u0000\u0000"+
		"\u0000\u007f\u0080\u0005e\u0000\u0000\u0080\u0081\u0005l\u0000\u0000\u0081"+
		"\u0082\u0005s\u0000\u0000\u0082\u0083\u0005e\u0000\u0000\u0083\f\u0001"+
		"\u0000\u0000\u0000\u0084\u0085\u0005w\u0000\u0000\u0085\u0086\u0005h\u0000"+
		"\u0000\u0086\u0087\u0005i\u0000\u0000\u0087\u0088\u0005l\u0000\u0000\u0088"+
		"\u0089\u0005e\u0000\u0000\u0089\u000e\u0001\u0000\u0000\u0000\u008a\u008b"+
		"\u0005b\u0000\u0000\u008b\u008c\u0005r\u0000\u0000\u008c\u008d\u0005e"+
		"\u0000\u0000\u008d\u008e\u0005a\u0000\u0000\u008e\u008f\u0005k\u0000\u0000"+
		"\u008f\u0010\u0001\u0000\u0000\u0000\u0090\u0091\u0005c\u0000\u0000\u0091"+
		"\u0092\u0005o\u0000\u0000\u0092\u0093\u0005n\u0000\u0000\u0093\u0094\u0005"+
		"t\u0000\u0000\u0094\u0095\u0005i\u0000\u0000\u0095\u0096\u0005n\u0000"+
		"\u0000\u0096\u0097\u0005u\u0000\u0000\u0097\u0098\u0005e\u0000\u0000\u0098"+
		"\u0012\u0001\u0000\u0000\u0000\u0099\u009a\u0005r\u0000\u0000\u009a\u009b"+
		"\u0005e\u0000\u0000\u009b\u009c\u0005t\u0000\u0000\u009c\u009d\u0005u"+
		"\u0000\u0000\u009d\u009e\u0005r\u0000\u0000\u009e\u009f\u0005n\u0000\u0000"+
		"\u009f\u0014\u0001\u0000\u0000\u0000\u00a0\u00a1\u0005;\u0000\u0000\u00a1"+
		"\u0016\u0001\u0000\u0000\u0000\u00a2\u00a3\u0005,\u0000\u0000\u00a3\u0018"+
		"\u0001\u0000\u0000\u0000\u00a4\u00a5\u0005(\u0000\u0000\u00a5\u001a\u0001"+
		"\u0000\u0000\u0000\u00a6\u00a7\u0005)\u0000\u0000\u00a7\u001c\u0001\u0000"+
		"\u0000\u0000\u00a8\u00a9\u0005{\u0000\u0000\u00a9\u001e\u0001\u0000\u0000"+
		"\u0000\u00aa\u00ab\u0005}\u0000\u0000\u00ab \u0001\u0000\u0000\u0000\u00ac"+
		"\u00ad\u0005[\u0000\u0000\u00ad\"\u0001\u0000\u0000\u0000\u00ae\u00af"+
		"\u0005]\u0000\u0000\u00af$\u0001\u0000\u0000\u0000\u00b0\u00b1\u0005="+
		"\u0000\u0000\u00b1&\u0001\u0000\u0000\u0000\u00b2\u00b3\u0005+\u0000\u0000"+
		"\u00b3(\u0001\u0000\u0000\u0000\u00b4\u00b5\u0005-\u0000\u0000\u00b5*"+
		"\u0001\u0000\u0000\u0000\u00b6\u00b7\u0005*\u0000\u0000\u00b7,\u0001\u0000"+
		"\u0000\u0000\u00b8\u00b9\u0005/\u0000\u0000\u00b9.\u0001\u0000\u0000\u0000"+
		"\u00ba\u00bb\u0005%\u0000\u0000\u00bb0\u0001\u0000\u0000\u0000\u00bc\u00bd"+
		"\u0005<\u0000\u0000\u00bd2\u0001\u0000\u0000\u0000\u00be\u00bf\u0005>"+
		"\u0000\u0000\u00bf4\u0001\u0000\u0000\u0000\u00c0\u00c1\u0005<\u0000\u0000"+
		"\u00c1\u00c2\u0005=\u0000\u0000\u00c26\u0001\u0000\u0000\u0000\u00c3\u00c4"+
		"\u0005>\u0000\u0000\u00c4\u00c5\u0005=\u0000\u0000\u00c58\u0001\u0000"+
		"\u0000\u0000\u00c6\u00c7\u0005=\u0000\u0000\u00c7\u00c8\u0005=\u0000\u0000"+
		"\u00c8:\u0001\u0000\u0000\u0000\u00c9\u00ca\u0005!\u0000\u0000\u00ca\u00cb"+
		"\u0005=\u0000\u0000\u00cb<\u0001\u0000\u0000\u0000\u00cc\u00cd\u0005&"+
		"\u0000\u0000\u00cd\u00ce\u0005&\u0000\u0000\u00ce>\u0001\u0000\u0000\u0000"+
		"\u00cf\u00d0\u0005|\u0000\u0000\u00d0\u00d1\u0005|\u0000\u0000\u00d1@"+
		"\u0001\u0000\u0000\u0000\u00d2\u00d3\u0005!\u0000\u0000\u00d3B\u0001\u0000"+
		"\u0000\u0000\u00d4\u00d5\u0007\u0000\u0000\u0000\u00d5D\u0001\u0000\u0000"+
		"\u0000\u00d6\u00d7\u0007\u0001\u0000\u0000\u00d7F\u0001\u0000\u0000\u0000"+
		"\u00d8\u00d9\u0007\u0002\u0000\u0000\u00d9H\u0001\u0000\u0000\u0000\u00da"+
		"\u00db\u0007\u0003\u0000\u0000\u00dbJ\u0001\u0000\u0000\u0000\u00dc\u00dd"+
		"\u0007\u0000\u0000\u0000\u00ddL\u0001\u0000\u0000\u0000\u00de\u00df\u0005"+
		"0\u0000\u0000\u00dfN\u0001\u0000\u0000\u0000\u00e0\u00e1\u0007\u0004\u0000"+
		"\u0000\u00e1P\u0001\u0000\u0000\u0000\u00e2\u00e3\u00050\u0000\u0000\u00e3"+
		"\u00e4\u0007\u0005\u0000\u0000\u00e4R\u0001\u0000\u0000\u0000\u00e5\u00e6"+
		"\u0007\u0006\u0000\u0000\u00e6T\u0001\u0000\u0000\u0000\u00e7\u00eb\u0003"+
		"G#\u0000\u00e8\u00ea\u0003I$\u0000\u00e9\u00e8\u0001\u0000\u0000\u0000"+
		"\u00ea\u00ed\u0001\u0000\u0000\u0000\u00eb\u00e9\u0001\u0000\u0000\u0000"+
		"\u00eb\u00ec\u0001\u0000\u0000\u0000\u00ecV\u0001\u0000\u0000\u0000\u00ed"+
		"\u00eb\u0001\u0000\u0000\u0000\u00ee\u00ef\u0007\u0007\u0000\u0000\u00ef"+
		"\u00f0\u0001\u0000\u0000\u0000\u00f0\u00f1\u0006+\u0000\u0000\u00f1X\u0001"+
		"\u0000\u0000\u0000\u00f2\u00f3\u0005/\u0000\u0000\u00f3\u00f4\u0005/\u0000"+
		"\u0000\u00f4\u00f8\u0001\u0000\u0000\u0000\u00f5\u00f7\t\u0000\u0000\u0000"+
		"\u00f6\u00f5\u0001\u0000\u0000\u0000\u00f7\u00fa\u0001\u0000\u0000\u0000"+
		"\u00f8\u00f9\u0001\u0000\u0000\u0000\u00f8\u00f6\u0001\u0000\u0000\u0000"+
		"\u00f9\u00fc\u0001\u0000\u0000\u0000\u00fa\u00f8\u0001\u0000\u0000\u0000"+
		"\u00fb\u00fd\u0005\r\u0000\u0000\u00fc\u00fb\u0001\u0000\u0000\u0000\u00fc"+
		"\u00fd\u0001\u0000\u0000\u0000\u00fd\u00fe\u0001\u0000\u0000\u0000\u00fe"+
		"\u00ff\u0005\n\u0000\u0000\u00ff\u0100\u0001\u0000\u0000\u0000\u0100\u0101"+
		"\u0006,\u0000\u0000\u0101Z\u0001\u0000\u0000\u0000\u0102\u0103\u0005/"+
		"\u0000\u0000\u0103\u0104\u0005*\u0000\u0000\u0104\u0108\u0001\u0000\u0000"+
		"\u0000\u0105\u0107\t\u0000\u0000\u0000\u0106\u0105\u0001\u0000\u0000\u0000"+
		"\u0107\u010a\u0001\u0000\u0000\u0000\u0108\u0109\u0001\u0000\u0000\u0000"+
		"\u0108\u0106\u0001\u0000\u0000\u0000\u0109\u010b\u0001\u0000\u0000\u0000"+
		"\u010a\u0108\u0001\u0000\u0000\u0000\u010b\u010c\u0005*\u0000\u0000\u010c"+
		"\u010d\u0005/\u0000\u0000\u010d\u010e\u0001\u0000\u0000\u0000\u010e\u010f"+
		"\u0006-\u0000\u0000\u010f\\\u0001\u0000\u0000\u0000\u0110\u0114\u0003"+
		"E\"\u0000\u0111\u0113\u0003C!\u0000\u0112\u0111\u0001\u0000\u0000\u0000"+
		"\u0113\u0116\u0001\u0000\u0000\u0000\u0114\u0112\u0001\u0000\u0000\u0000"+
		"\u0114\u0115\u0001\u0000\u0000\u0000\u0115^\u0001\u0000\u0000\u0000\u0116"+
		"\u0114\u0001\u0000\u0000\u0000\u0117\u0119\u0003M&\u0000\u0118\u011a\u0003"+
		"O\'\u0000\u0119\u0118\u0001\u0000\u0000\u0000\u011a\u011b\u0001\u0000"+
		"\u0000\u0000\u011b\u0119\u0001\u0000\u0000\u0000\u011b\u011c\u0001\u0000"+
		"\u0000\u0000\u011c`\u0001\u0000\u0000\u0000\u011d\u011f\u0003Q(\u0000"+
		"\u011e\u0120\u0003S)\u0000\u011f\u011e\u0001\u0000\u0000\u0000\u0120\u0121"+
		"\u0001\u0000\u0000\u0000\u0121\u011f\u0001\u0000\u0000\u0000\u0121\u0122"+
		"\u0001\u0000\u0000\u0000\u0122b\u0001\u0000\u0000\u0000\u0123\u0127\u0003"+
		"].\u0000\u0124\u0127\u0003_/\u0000\u0125\u0127\u0003a0\u0000\u0126\u0123"+
		"\u0001\u0000\u0000\u0000\u0126\u0124\u0001\u0000\u0000\u0000\u0126\u0125"+
		"\u0001\u0000\u0000\u0000\u0127d\u0001\u0000\u0000\u0000\u0128\u012a\u0003"+
		"].\u0000\u0129\u0128\u0001\u0000\u0000\u0000\u0129\u012a\u0001\u0000\u0000"+
		"\u0000\u012a\u012b\u0001\u0000\u0000\u0000\u012b\u012d\u0005.\u0000\u0000"+
		"\u012c\u012e\u0003].\u0000\u012d\u012c\u0001\u0000\u0000\u0000\u012e\u012f"+
		"\u0001\u0000\u0000\u0000\u012f\u012d\u0001\u0000\u0000\u0000\u012f\u0130"+
		"\u0001\u0000\u0000\u0000\u0130f\u0001\u0000\u0000\u0000\u000b\u0000\u00eb"+
		"\u00f8\u00fc\u0108\u0114\u011b\u0121\u0126\u0129\u012f\u0001\u0006\u0000"+
		"\u0000";
	public static final ATN _ATN =
		new ATNDeserializer().deserialize(_serializedATN.toCharArray());
	static {
		_decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
		for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
			_decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
		}
	}
}