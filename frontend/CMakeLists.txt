# Generate ANTLR files
set(ANTLR_EXECUTABLE antlr4)
set(GRAMMAR_FILE ${CMAKE_CURRENT_SOURCE_DIR}/grammar/SysY2022.g4)
set(GENERATED_DIR ${CMAKE_CURRENT_SOURCE_DIR}/generated)

# Create generated directory
file(MAKE_DIRECTORY ${GENERATED_DIR})

# Generate ANTLR files
add_custom_command(
    OUTPUT
        ${GENERATED_DIR}/SysY2022Lexer.cpp
        ${GENERATED_DIR}/SysY2022Parser.cpp
        ${GENERATED_DIR}/SysY2022BaseVisitor.cpp
        ${GENERATED_DIR}/SysY2022BaseListener.cpp
        ${GENERATED_DIR}/SysY2022Visitor.cpp
        ${GENERATED_DIR}/SysY2022Listener.cpp
    COMMAND ${ANTLR_EXECUTABLE} -Dlanguage=Cpp -visitor -listener -o ${GENERATED_DIR} ${G<PERSON><PERSON>AR_FILE}
    DEPENDS ${G<PERSON><PERSON><PERSON>_FILE}
    COMMENT "Generating ANTLR files"
)

# Frontend library
add_library(frontend
    ${GENERATED_DIR}/SysY2022Lexer.cpp
    ${GENERATED_DIR}/SysY2022Parser.cpp
    ${GENERATED_DIR}/SysY2022BaseVisitor.cpp
    ${GENERATED_DIR}/SysY2022BaseListener.cpp
    ${GENERATED_DIR}/SysY2022Visitor.cpp
    ${GENERATED_DIR}/SysY2022Listener.cpp
)

target_include_directories(frontend PUBLIC
    ${GENERATED_DIR}
    ${ANTLR4_INCLUDE_DIR}
)

target_link_libraries(frontend
    ${ANTLR4_LIBRARIES}
)