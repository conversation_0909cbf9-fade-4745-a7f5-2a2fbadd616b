#include <iostream>

#include "antlr4-runtime.h"
#include "SysY2022Lexer.h"
#include "SysY2022Parser.h"
#include "../middle_end/ASTVisitor.hpp"
// #include "main.h"

using namespace antlr4;

int main(int argc, const char* argv[]) {
    std::ifstream stream;
    stream.open(argv[1]);
    ANTLRInputStream input(stream);
    //ANTLRInputStream input(std::cin);

    // Lexer
    SysY2022Lexer lexer(&input);
    CommonTokenStream tokens(&lexer);

    tokens.fill();
   
    for (auto token : tokens.getTokens()) {
  
        //简单粗暴的输出token信息并不符合题目要求
        std::cout << token->toString() << std::endl;
    }

    /* 语法分析
    sysyparser parser(&tokens);
    tree::parsetree* tree = parser.compunit();

    std::cout << tree->tostringtree(&parser) << std::endl << std::endl;
    */
    SysY2022Parser parser(&tokens);
    tree::ParseTree* tree = parser.compUnit();

    std::cout << tree->toStringTree(&parser) << std::endl << std::endl;


    std::cout << "=== AST Structure ===" << std::endl;
    ASTVisitor visitor;
    visitor.visit(tree);

    return 0;
}    