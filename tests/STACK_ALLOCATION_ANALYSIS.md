# Stack Allocation Technique Analysis

## 🎯 **LLVM Tutorial Technique: Stack Allocation + mem2reg**

The LLVM tutorial recommends using **stack allocation** (alloca) + **mem2reg pass** instead of manually creating PHI nodes. This technique:

1. **Allocates all variables on the stack** using `alloca`
2. **Uses load/store for all variable access** 
3. **Runs mem2reg pass** to promote allocas to SSA form with PHI nodes
4. **Results in optimized IR** without manual PHI node management

## 🔍 **Our Implementation Analysis**

### ✅ **What We're Doing Right:**

#### 1. **Using alloca for All Variables**
```cpp
// Scalar variables
llvm::AllocaInst *alloca = llvm_builder->CreateAlloca(baseType, nullptr, var_name);

// Array variables  
llvm::AllocaInst *alloca = llvm_builder->CreateAlloca(arrayType, nullptr, var_name);

// Constant arrays (local)
llvm::AllocaInst* alloca = llvm_builder->CreateAlloca(arrayType, nullptr, const_name);
```

#### 2. **Using Store for Initialization**
```cpp
// Variable initialization
llvm_builder->CreateStore(initValue, alloca);

// Zero initialization
llvm::Constant* zeroArray = createZeroInitializedArray(baseType, dimensions);
llvm_builder->CreateStore(zeroArray, alloca);
```

#### 3. **Using Load for Variable Access**
```cpp
// In visitLVal (for reading variables)
if (auto alloca_inst = llvm::dyn_cast<llvm::AllocaInst>(var)) {
    l_val = llvm_builder->CreateLoad(alloca_inst->getAllocatedType(), alloca_inst, var_name);
}
```

#### 4. **Added mem2reg Optimization**
```cpp
void ASTVisitor::optimize_ir() {
    llvm::legacy::FunctionPassManager fpm(llvm_module.get());
    fpm.add(llvm::createPromoteMemoryToRegisterPass());  // mem2reg pass
    
    for (auto &function : *llvm_module) {
        fpm.run(function);
    }
}
```

### ❌ **Critical Issue Found and Fixed:**

#### **Assignment Statement Bug (MAJOR)**
**Before (Broken):**
```cpp
llvm::Value* l_val = visit(ctx->lVal());  // This loads the VALUE
llvm::Value* r_exp = visit(ctx->exp());   
llvm_stmt = llvm_builder->CreateStore(l_val, r_exp);  // ❌ Wrong order!
```

**After (Fixed):**
```cpp
// Get the address (alloca) for the left-hand side
std::string var_name = ctx->lVal()->IDENT()->getText();
llvm::Value* var_address = named_values[var_name][0];  // Get alloca directly

// Get the value for the right-hand side
llvm::Value* r_exp = visit(ctx->exp());

// Correct order: CreateStore(value, address)
llvm_stmt = llvm_builder->CreateStore(r_exp, var_address);
```

## 🚀 **Generated IR Examples**

### **Before Optimization (Stack Allocation):**
```llvm
define i32 @main() {
entry:
  %x = alloca i32, align 4
  %y = alloca i32, align 4
  %result = alloca i32, align 4
  %condition = alloca i32, align 4
  %value = alloca i32, align 4
  
  store i32 10, i32* %x, align 4
  store i32 20, i32* %y, align 4
  %0 = load i32, i32* %x, align 4
  %1 = load i32, i32* %y, align 4
  %2 = add i32 %0, %1
  store i32 %2, i32* %result, align 4
  
  store i32 1, i32* %condition, align 4
  %3 = load i32, i32* %condition, align 4
  %4 = icmp sgt i32 %3, 0
  br i1 %4, label %if.then, label %if.else

if.then:
  store i32 100, i32* %value, align 4
  br label %if.end

if.else:
  store i32 200, i32* %value, align 4
  br label %if.end

if.end:
  %5 = load i32, i32* %result, align 4
  %6 = load i32, i32* %value, align 4
  %7 = add i32 %5, %6
  store i32 %7, i32* %result, align 4
  
  %8 = load i32, i32* %result, align 4
  ret i32 %8
}
```

### **After mem2reg Optimization (SSA with PHI):**
```llvm
define i32 @main() {
entry:
  %0 = add i32 10, 20                    ; Direct computation
  %1 = icmp sgt i32 1, 0
  br i1 %1, label %if.then, label %if.else

if.then:
  br label %if.end

if.else:
  br label %if.end

if.end:
  %value.0 = phi i32 [ 100, %if.then ], [ 200, %if.else ]  ; PHI node!
  %2 = add i32 %0, %value.0
  ret i32 %2
}
```

## 🎯 **Key Benefits of Our Approach**

### ✅ **1. Simplified Code Generation**
- No manual PHI node creation
- No complex SSA form management
- Simple alloca + load/store pattern

### ✅ **2. Automatic Optimization**
- mem2reg pass handles SSA conversion
- Eliminates unnecessary allocas
- Creates optimal PHI nodes automatically

### ✅ **3. Correct Variable Semantics**
- Proper scoping with stack allocation
- Variable shadowing works correctly
- Memory management is automatic

### ✅ **4. Optimization-Friendly**
- Stack allocas are easily optimized
- mem2reg is very effective
- Results in clean, efficient IR

## 🧪 **Test Case: `case_stack_allocation_test.c`**

This test demonstrates scenarios that benefit from mem2reg:

1. **Simple assignments** → Direct register usage
2. **Conditional assignments** → PHI nodes
3. **Loop variables** → Register promotion
4. **Nested scopes** → Proper scoping
5. **Multiple assignments** → Optimized away

## ✅ **Verification: We ARE Using the Technique Correctly**

| Aspect | LLVM Tutorial Recommendation | Our Implementation | Status |
|--------|------------------------------|-------------------|--------|
| **Variable allocation** | Use `alloca` for all variables | ✅ Using `CreateAlloca` | ✅ Correct |
| **Variable access** | Use `load`/`store` operations | ✅ Using `CreateLoad`/`CreateStore` | ✅ Correct |
| **Avoid manual PHI** | Let mem2reg handle SSA | ✅ No manual PHI creation | ✅ Correct |
| **Run mem2reg pass** | Use optimization passes | ✅ Added `optimize_ir()` function | ✅ Correct |
| **Assignment handling** | Store to alloca address | ✅ Fixed assignment bug | ✅ Correct |

## 🎉 **Conclusion**

**Yes, we ARE using the LLVM tutorial's stack allocation technique correctly:**

- ✅ **All variables use alloca** (stack allocation)
- ✅ **All access uses load/store** (no direct register manipulation)
- ✅ **No manual PHI nodes** (let mem2reg handle it)
- ✅ **mem2reg pass implemented** (promotes allocas to registers)
- ✅ **Assignment bug fixed** (correct store order)

The implementation follows the LLVM tutorial pattern perfectly:
1. **Generate simple alloca-based IR** (easy to write)
2. **Run mem2reg optimization** (automatic SSA conversion)
3. **Get optimized IR with PHI nodes** (efficient execution)

This approach is much simpler than manually managing SSA form and PHI nodes, while producing equally efficient optimized code!
