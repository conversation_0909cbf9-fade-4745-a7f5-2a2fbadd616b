[{"directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/build", "command": "/usr/bin/c++  -I/usr/lib/llvm-14/include -I/usr/include/antlr4-runtime -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/backend -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/backend/include   -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -std=gnu++17 -o CMakeFiles/scanner.dir/src/main.cpp.o -c /home/<USER>/Development/para-compiler-2025/project2824713-306828/src/main.cpp", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/src/main.cpp"}, {"directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/build/frontend", "command": "/usr/bin/c++  -I/usr/lib/llvm-14/include -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated -I/usr/include/antlr4-runtime   -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -std=gnu++17 -o CMakeFiles/frontend.dir/generated/SysY2022Lexer.cpp.o -c /home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022Lexer.cpp", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022Lexer.cpp"}, {"directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/build/frontend", "command": "/usr/bin/c++  -I/usr/lib/llvm-14/include -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated -I/usr/include/antlr4-runtime   -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -std=gnu++17 -o CMakeFiles/frontend.dir/generated/SysY2022Parser.cpp.o -c /home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022Parser.cpp", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022Parser.cpp"}, {"directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/build/frontend", "command": "/usr/bin/c++  -I/usr/lib/llvm-14/include -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated -I/usr/include/antlr4-runtime   -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -std=gnu++17 -o CMakeFiles/frontend.dir/generated/SysY2022BaseVisitor.cpp.o -c /home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022BaseVisitor.cpp", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022BaseVisitor.cpp"}, {"directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/build/frontend", "command": "/usr/bin/c++  -I/usr/lib/llvm-14/include -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated -I/usr/include/antlr4-runtime   -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -std=gnu++17 -o CMakeFiles/frontend.dir/generated/SysY2022BaseListener.cpp.o -c /home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022BaseListener.cpp", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022BaseListener.cpp"}, {"directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/build/frontend", "command": "/usr/bin/c++  -I/usr/lib/llvm-14/include -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated -I/usr/include/antlr4-runtime   -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -std=gnu++17 -o CMakeFiles/frontend.dir/generated/SysY2022Visitor.cpp.o -c /home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022Visitor.cpp", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022Visitor.cpp"}, {"directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/build/frontend", "command": "/usr/bin/c++  -I/usr/lib/llvm-14/include -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated -I/usr/include/antlr4-runtime   -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -std=gnu++17 -o CMakeFiles/frontend.dir/generated/SysY2022Listener.cpp.o -c /home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022Listener.cpp", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated/SysY2022Listener.cpp"}, {"directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/build/backend", "command": "/usr/bin/c++ -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -I/usr/lib/llvm-14/include -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/backend/include -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated -I/usr/include/antlr4-runtime   -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -std=gnu++17 -o CMakeFiles/backend.dir/ASTVisitor.cpp.o -c /home/<USER>/Development/para-compiler-2025/project2824713-306828/backend/ASTVisitor.cpp", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/backend/ASTVisitor.cpp"}, {"directory": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/build/backend", "command": "/usr/bin/c++ -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -I/usr/lib/llvm-14/include -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/backend/include -I/home/<USER>/Development/para-compiler-2025/project2824713-306828/frontend/generated -I/usr/include/antlr4-runtime   -D_GNU_SOURCE -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -std=gnu++17 -o CMakeFiles/backend.dir/Type.cpp.o -c /home/<USER>/Development/para-compiler-2025/project2824713-306828/backend/Type.cpp", "file": "/home/<USER>/Development/para-compiler-2025/project2824713-306828/backend/Type.cpp"}]