#pragma once
#include "antlr4-runtime.h"
#include "../generated_front_end/SysY2022BaseVisitor.h"
#include <iostream>
#include <memory>

// LLVM includes
#include "llvm/IR/LLVMContext.h"
#include "llvm/IR/Module.h"
#include "llvm/IR/IRBuilder.h"
#include "llvm/IR/Value.h"
#include "llvm/IR/Function.h"
#include "llvm/IR/BasicBlock.h"
#include "llvm/IR/Type.h"
#include "llvm/IR/Verifier.h"
#include "llvm/Support/raw_ostream.h"


class ASTVisitor : public SysY2022BaseVisitor {
private:
    int indent = 0;

    // LLVM components
    std::unique_ptr<llvm::LLVMContext> llvm_context;
    std::unique_ptr<llvm::Module> llvm_module;
    std::unique_ptr<llvm::IRBuilder<>> llvm_builder;

    // Symbol table for variables
    std::map<std::string, llvm::Value*> named_values;

    void printIndent() {
        for (int i = 0; i < indent; i++) std::cout << "  ";
    }

public:
    ASTVisitor();
    void generate_llvm_ir();
    void print_llvm_ir();


    antlrcpp::Any visitCompUnit(SysY2022Parser::CompUnitContext *ctx) override;
    antlrcpp::Any visitFuncDef(SysY2022Parser::FuncDefContext *ctx) override;
    antlrcpp::Any visitBlock(SysY2022Parser::BlockContext *ctx) override;
    antlrcpp::Any visitVarDecl(SysY2022Parser::VarDeclContext *ctx) override;
    antlrcpp::Any visitStmt(SysY2022Parser::StmtContext *ctx) override;
    antlrcpp::Any visitExp(SysY2022Parser::ExpContext *ctx) override;
    antlrcpp::Any visitAddExp(SysY2022Parser::AddExpContext *ctx) override;
    antlrcpp::Any visitNumber(SysY2022Parser::NumberContext *ctx) override;
    antlrcpp::Any visitLVal(SysY2022Parser::LValContext *ctx) override;
    // Add more visit methods as needed
};