# Generated ANTLR files
antlr4/generated_front_end/
antlr4/.antlr/

# Compiled object files
*.o
*.obj

# Executables
scanner
antlr4/scanner

# Build artifacts
*.a
*.so
*.dylib

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log

# Flex/Bison generated files
*.tab.c
*.tab.h
lex.yy.c
*.output

# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
!antlr4/Makefile
!flex_bison/Makefile

# Test outputs
tests/output/
*.out