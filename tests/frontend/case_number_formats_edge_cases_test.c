// Test edge cases for different number formats
int main() {
    // Test 1: Minimum values for each format
    int min_hex = 0x1;          // Smallest hex
    int min_oct = 01;           // Smallest octal
    int min_dec = 1;            // Smallest decimal
    
    // Test 2: Maximum typical values
    int max_hex = 0x7FFFFFFF;   // Max positive 32-bit int in hex
    int max_oct = 017777777777; // Max positive 32-bit int in octal
    int max_dec = 2147483647;   // Max positive 32-bit int in decimal
    
    // Test 3: Common hex values
    int common_hex1 = 0x100;    // 256
    int common_hex2 = 0x1000;   // 4096
    int common_hex3 = 0x10000;  // 65536
    
    // Test 4: Common octal values
    int common_oct1 = 0100;     // 64
    int common_oct2 = 01000;    // 512
    int common_oct3 = 010000;   // 4096
    
    // Test 5: Powers of 2 in different formats
    int pow2_hex1 = 0x2;        // 2
    int pow2_hex2 = 0x4;        // 4
    int pow2_hex3 = 0x8;        // 8
    int pow2_hex4 = 0x10;       // 16
    int pow2_hex5 = 0x20;       // 32
    
    int pow2_oct1 = 02;         // 2
    int pow2_oct2 = 04;         // 4
    int pow2_oct3 = 010;        // 8
    int pow2_oct4 = 020;        // 16
    int pow2_oct5 = 040;        // 32
    
    // Test 6: Hex digits A-F (both cases)
    int hex_a = 0xa;            // 10
    int hex_b = 0xb;            // 11
    int hex_c = 0xc;            // 12
    int hex_d = 0xd;            // 13
    int hex_e = 0xe;            // 14
    int hex_f = 0xf;            // 15
    
    int hex_A = 0xA;            // 10
    int hex_B = 0xB;            // 11
    int hex_C = 0xC;            // 12
    int hex_D = 0xD;            // 13
    int hex_E = 0xE;            // 14
    int hex_F = 0xF;            // 15
    
    // Test 7: Octal digits 0-7
    int oct_0 = 00;             // 0
    int oct_1 = 01;             // 1
    int oct_2 = 02;             // 2
    int oct_3 = 03;             // 3
    int oct_4 = 04;             // 4
    int oct_5 = 05;             // 5
    int oct_6 = 06;             // 6
    int oct_7 = 07;             // 7
    
    // Test 8: Mixed format arithmetic
    int mixed1 = 0x10 + 010;    // 16 + 8 = 24
    int mixed2 = 0xFF - 077;    // 255 - 63 = 192
    int mixed3 = 0x8 * 010;     // 8 * 8 = 64
    
    // Test 9: Use in control flow
    int result = 0;
    if (0x10 > 010) {           // 16 > 8
        result = result + 0x1;  // Add 1
    }
    
    if (077 == 0x3F) {          // 63 == 63
        result = result + 0x2;  // Add 2
    }
    
    // Test 10: Loop with different formats
    int i = 0;
    while (i < 0x5) {           // Loop 5 times
        result = result + 010;  // Add 8 each time
        i = i + 1;
    }
    // result should be 3 + (8 * 5) = 43
    
    // Test 11: Array indexing with different formats
    int array[0x10] = {0x1, 0x2, 0x3, 0x4, 0x5};  // Array of size 16
    result = result + array[0x0];   // Add array[0] = 1
    result = result + array[01];    // Add array[1] = 2
    result = result + array[2];     // Add array[2] = 3
    // result should be 43 + 1 + 2 + 3 = 49
    
    return result;
}
